#!/usr/bin/env python3
"""
安全删除Kafka消费者组的完整示例脚本

演示如何正确停止消费者进程并安全删除消费者组
包含完整的错误处理和日志记录
"""

import os
import sys
import time
import signal
import logging
import subprocess
from pathlib import Path
from typing import Optional, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.kafka_consumer_group_manager import KafkaConsumerGroupManager
from kafka_data_filter.config_manager import ConfigManager


class SafeConsumerGroupDeletion:
    """安全消费者组删除管理器"""
    
    def __init__(self, config_path: str):
        """
        初始化删除管理器
        
        参数:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.logger = self._setup_logging()
        
        # 加载配置
        self.config_manager = ConfigManager(config_path)
        kafka_config = self.config_manager.get_kafka_config()
        
        # 初始化消费者组管理器
        self.group_manager = KafkaConsumerGroupManager(
            bootstrap_servers=kafka_config['bootstrap_servers'],
            **{k: v for k, v in kafka_config.items() if k != 'bootstrap_servers'}
        )
        
        self.group_id = kafka_config.get('group_id', 'kafka-data-filter')
        self.logger.info(f"初始化完成，目标消费者组: {self.group_id}")
    
    def _setup_logging(self) -> logging.Logger:
        """配置日志系统"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('consumer_group_deletion.log', encoding='utf-8')
            ]
        )
        return logging.getLogger(__name__)
    
    def find_consumer_processes(self) -> List[dict]:
        """
        查找相关的消费者进程
        
        返回:
            进程信息列表
        """
        try:
            # 使用ps命令查找相关进程
            cmd = ["ps", "aux"]
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            processes = []
            for line in result.stdout.split('\n'):
                if 'kafka-data-filter' in line or 'main.py' in line:
                    parts = line.split()
                    if len(parts) >= 11:
                        processes.append({
                            'pid': parts[1],
                            'command': ' '.join(parts[10:]),
                            'user': parts[0]
                        })
            
            return processes
            
        except Exception as e:
            self.logger.error(f"查找消费者进程失败: {e}")
            return []
    
    def stop_consumer_processes(self, processes: List[dict], 
                              timeout_seconds: int = 30) -> bool:
        """
        停止消费者进程
        
        参数:
            processes: 进程信息列表
            timeout_seconds: 停止超时时间
            
        返回:
            是否成功停止所有进程
        """
        if not processes:
            self.logger.info("未找到需要停止的消费者进程")
            return True
        
        self.logger.info(f"找到 {len(processes)} 个消费者进程，开始停止...")
        
        # 发送SIGTERM信号进行优雅关闭
        for process in processes:
            try:
                pid = int(process['pid'])
                self.logger.info(f"向进程 {pid} 发送SIGTERM信号")
                os.kill(pid, signal.SIGTERM)
            except Exception as e:
                self.logger.error(f"发送SIGTERM信号失败 (PID: {process['pid']}): {e}")
        
        # 等待进程停止
        start_time = time.time()
        while time.time() - start_time < timeout_seconds:
            remaining_processes = self.find_consumer_processes()
            if not remaining_processes:
                self.logger.info("所有消费者进程已停止")
                return True
            
            self.logger.info(f"仍有 {len(remaining_processes)} 个进程运行，继续等待...")
            time.sleep(2)
        
        # 如果优雅关闭失败，发送SIGKILL信号
        remaining_processes = self.find_consumer_processes()
        if remaining_processes:
            self.logger.warning("优雅关闭超时，执行强制终止...")
            for process in remaining_processes:
                try:
                    pid = int(process['pid'])
                    self.logger.warning(f"强制终止进程 {pid}")
                    os.kill(pid, signal.SIGKILL)
                except Exception as e:
                    self.logger.error(f"强制终止进程失败 (PID: {process['pid']}): {e}")
            
            time.sleep(2)
            final_processes = self.find_consumer_processes()
            if final_processes:
                self.logger.error("部分进程无法停止")
                return False
        
        self.logger.info("所有消费者进程已停止")
        return True
    
    def wait_for_group_stabilization(self, max_wait_time: int = 120) -> bool:
        """
        等待消费者组状态稳定
        
        参数:
            max_wait_time: 最大等待时间（秒）
            
        返回:
            是否达到稳定状态
        """
        self.logger.info(f"等待消费者组 {self.group_id} 状态稳定...")
        
        start_time = time.time()
        while time.time() - start_time < max_wait_time:
            try:
                group_info = self.group_manager.get_consumer_group_info(self.group_id)
                
                if not group_info.get('exists', False):
                    self.logger.info("消费者组不存在")
                    return True
                
                state = group_info.get('state', 'Unknown')
                member_count = group_info.get('member_count', 0)
                
                self.logger.info(f"消费者组状态: {state}, 成员数: {member_count}")
                
                if state in ['Empty', 'Dead'] and member_count == 0:
                    self.logger.info("消费者组已达到稳定状态")
                    return True
                
                time.sleep(5)
                
            except Exception as e:
                self.logger.warning(f"检查消费者组状态时出错: {e}")
                time.sleep(5)
        
        self.logger.warning("等待消费者组稳定超时")
        return False
    
    def perform_safe_deletion(self, force: bool = False) -> bool:
        """
        执行安全删除流程
        
        参数:
            force: 是否强制删除
            
        返回:
            是否删除成功
        """
        try:
            self.logger.info("=" * 60)
            self.logger.info("开始安全删除消费者组流程")
            self.logger.info("=" * 60)
            
            # 步骤1：查找并停止消费者进程
            self.logger.info("步骤1: 查找并停止消费者进程")
            processes = self.find_consumer_processes()
            if processes:
                self.logger.info(f"找到 {len(processes)} 个相关进程:")
                for process in processes:
                    self.logger.info(f"  PID: {process['pid']}, 命令: {process['command']}")
                
                if not self.stop_consumer_processes(processes):
                    self.logger.error("停止消费者进程失败")
                    if not force:
                        return False
            else:
                self.logger.info("未找到运行中的消费者进程")
            
            # 步骤2：等待消费者组状态稳定
            self.logger.info("步骤2: 等待消费者组状态稳定")
            if not self.wait_for_group_stabilization():
                self.logger.warning("消费者组状态未能稳定")
                if not force:
                    return False
            
            # 步骤3：安全检查
            self.logger.info("步骤3: 执行安全检查")
            is_safe, warnings = self.group_manager.check_group_safety_for_deletion(self.group_id)
            
            if warnings:
                self.logger.warning("安全检查发现以下警告:")
                for warning in warnings:
                    self.logger.warning(f"  - {warning}")
            
            if not is_safe and not force:
                self.logger.error("安全检查未通过，使用 force=True 可强制删除")
                return False
            
            # 步骤4：备份消费者组信息
            self.logger.info("步骤4: 备份消费者组信息")
            try:
                backup_path = self.group_manager.backup_consumer_group_offsets(self.group_id)
                self.logger.info(f"备份完成: {backup_path}")
            except Exception as e:
                self.logger.warning(f"备份失败: {e}")
                if not force:
                    return False
            
            # 步骤5：执行删除
            self.logger.info("步骤5: 执行删除操作")
            success = self.group_manager.delete_consumer_group(
                group_id=self.group_id,
                force=force,
                backup=False  # 已经备份过了
            )
            
            if not success:
                self.logger.error("删除操作失败")
                return False
            
            # 步骤6：验证删除结果
            self.logger.info("步骤6: 验证删除结果")
            if self.group_manager.verify_group_deletion(self.group_id):
                self.logger.info("✅ 消费者组删除成功并已验证")
                return True
            else:
                self.logger.error("❌ 删除验证失败")
                return False
            
        except Exception as e:
            self.logger.error(f"安全删除流程失败: {e}")
            return False
        finally:
            self.logger.info("=" * 60)
    
    def close(self) -> None:
        """关闭管理器"""
        if self.group_manager:
            self.group_manager.close()


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="安全删除Kafka消费者组",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  # 安全删除消费者组
  python safe_consumer_group_deletion.py filter_config.yaml
  
  # 强制删除消费者组
  python safe_consumer_group_deletion.py filter_config.yaml --force
        """
    )
    
    parser.add_argument(
        "config",
        help="配置文件路径"
    )
    
    parser.add_argument(
        "--force",
        action="store_true",
        help="强制删除（跳过安全检查）"
    )
    
    args = parser.parse_args()
    
    # 检查配置文件
    if not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        sys.exit(1)
    
    # 创建删除管理器
    try:
        deletion_manager = SafeConsumerGroupDeletion(args.config)
    except Exception as e:
        print(f"初始化失败: {e}")
        sys.exit(1)
    
    try:
        # 执行安全删除
        success = deletion_manager.perform_safe_deletion(force=args.force)
        
        if success:
            print("\n✅ 消费者组删除成功")
            sys.exit(0)
        else:
            print("\n❌ 消费者组删除失败")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n操作被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)
    finally:
        deletion_manager.close()


if __name__ == "__main__":
    main()
