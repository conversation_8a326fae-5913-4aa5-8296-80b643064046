# Kafka消费者组自动清理功能使用指南

本指南详细说明如何使用Kafka数据过滤程序的自动清理功能，确保程序关闭后自动删除消费者组。

## 功能概述

自动清理功能会在以下情况下自动删除消费者组：
- 程序正常退出时
- 程序收到终止信号时（SIGTERM、SIGINT等）
- 程序异常退出时（通过atexit处理器）

## 配置说明

### 1. 配置文件设置

在 `filter_config.yaml` 中添加自动清理配置：

```yaml
# 自动清理配置
auto_cleanup:
  # 是否启用程序退出时自动删除消费者组
  enabled: true
  
  # 清理操作超时时间（秒）
  timeout: 30
  
  # 清理前是否备份消费者组信息
  backup_before_cleanup: true
  
  # 是否在异常退出时也执行清理（建议启用）
  cleanup_on_signal: true
```

### 2. 配置参数说明

- **enabled**: 是否启用自动清理功能
- **timeout**: 清理操作的最大等待时间
- **backup_before_cleanup**: 删除前是否备份消费者组的offset信息
- **cleanup_on_signal**: 是否在收到系统信号时执行清理

## 使用方法

### 1. 程序集成使用

自动清理功能已集成到主程序中，无需额外操作：

```bash
# 正常运行程序，退出时会自动清理
python main.py filter_config.yaml

# 使用Ctrl+C停止程序，也会自动清理
python main.py filter_config.yaml
# 按 Ctrl+C
```

### 2. 手动清理

在程序运行过程中，也可以手动触发清理：

```python
from kafka_data_filter.main import KafkaDataFilterApp

app = KafkaDataFilterApp("filter_config.yaml")

# 手动清理消费者组
success = app.manual_cleanup_consumer_group(force=True)
if success:
    print("清理成功")
else:
    print("清理失败")
```

### 3. 独立清理脚本

使用独立的清理脚本在程序外部清理：

```bash
# 从配置文件清理消费者组
python cleanup_consumer_group.py --config filter_config.yaml

# 清理指定的消费者组
python cleanup_consumer_group.py --bootstrap-servers 123.235.23.238:19301 --group moye-check-data

# 批量清理多个消费者组
python cleanup_consumer_group.py --config filter_config.yaml --groups group1,group2,group3

# 强制清理（跳过安全检查）
python cleanup_consumer_group.py --config filter_config.yaml --force

# 清理时跳过备份
python cleanup_consumer_group.py --config filter_config.yaml --no-backup
```

## 安全保障

### 1. 自动安全检查

清理前会自动执行以下检查：
- 消费者组是否存在
- 消费者组状态是否适合删除
- 是否有活跃的消费者实例
- 是否有未处理的消息（lag检查）

### 2. 备份机制

删除前会自动备份消费者组信息：
```json
{
  "group_id": "moye-check-data",
  "backup_time": "2023-12-01T10:30:00",
  "group_info": {
    "state": "Empty",
    "member_count": 0,
    "offsets": {...}
  },
  "kafka_cluster": "123.235.23.238:19301"
}
```

### 3. 错误处理

- 清理失败不会影响程序正常退出
- 详细的错误日志记录
- 超时保护机制

## 测试验证

### 1. 功能测试

使用测试脚本验证自动清理功能：

```bash
# 测试正常退出时的自动清理
python test_auto_cleanup.py normal

# 测试信号退出时的自动清理
python test_auto_cleanup.py signal

# 测试手动清理功能
python test_auto_cleanup.py manual

# 交互式测试
python test_auto_cleanup.py interactive
```

### 2. 状态监控

检查清理状态：

```python
from kafka_data_filter.main import KafkaDataFilterApp

app = KafkaDataFilterApp("filter_config.yaml")
status = app.get_cleanup_status()
print(status)
```

输出示例：
```python
{
    'group_id': 'moye-check-data',
    'cleanup_on_exit': True,
    'cleanup_executed': False,
    'backup_before_cleanup': True,
    'cleanup_timeout': 30,
    'manager_initialized': True
}
```

## 日志记录

自动清理过程会产生详细的日志：

```
2023-12-01 10:30:00 - auto_cleanup_manager - INFO - 自动清理管理器已初始化，目标消费者组: moye-check-data
2023-12-01 10:30:05 - auto_cleanup_manager - INFO - 程序正常退出，执行自动清理...
2023-12-01 10:30:05 - auto_cleanup_manager - INFO - 开始执行消费者组清理操作，原因: 正常退出
2023-12-01 10:30:06 - auto_cleanup_manager - INFO - 等待消费者组变为空状态...
2023-12-01 10:30:07 - auto_cleanup_manager - INFO - 消费者组信息已备份到: auto_cleanup_backup_moye-check-data_20231201_103007.json
2023-12-01 10:30:08 - auto_cleanup_manager - INFO - 删除消费者组: moye-check-data
2023-12-01 10:30:09 - auto_cleanup_manager - INFO - ✅ 消费者组 moye-check-data 自动清理成功
2023-12-01 10:30:10 - auto_cleanup_manager - INFO - ✅ 删除验证通过
```

## 最佳实践

### 1. 生产环境配置

```yaml
auto_cleanup:
  enabled: true
  timeout: 60  # 生产环境建议更长的超时时间
  backup_before_cleanup: true  # 生产环境必须备份
  cleanup_on_signal: true
```

### 2. 开发环境配置

```yaml
auto_cleanup:
  enabled: true
  timeout: 15  # 开发环境可以使用较短的超时时间
  backup_before_cleanup: false  # 开发环境可以跳过备份
  cleanup_on_signal: true
```

### 3. 监控建议

- 定期检查备份文件
- 监控清理日志
- 设置清理失败告警
- 定期验证消费者组状态

## 故障排除

### 1. 清理失败

**问题**: 自动清理失败，消费者组仍然存在

**解决方案**:
```bash
# 手动强制清理
python cleanup_consumer_group.py --config filter_config.yaml --force

# 检查Kafka集群状态
python consumer_group_admin.py --config filter_config.yaml list

# 查看详细错误日志
tail -f kafka_data_filter.log
```

### 2. 权限问题

**问题**: 没有权限删除消费者组

**解决方案**:
- 检查Kafka ACL配置
- 确认用户具有删除权限
- 联系Kafka管理员

### 3. 网络问题

**问题**: 网络连接导致清理超时

**解决方案**:
- 增加timeout配置
- 检查网络连接
- 使用独立清理脚本重试

## 注意事项

1. **数据安全**: 删除消费者组会丢失消费进度，确保数据已正确处理
2. **集群影响**: 清理操作不会影响其他消费者组或Kafka集群稳定性
3. **备份重要性**: 生产环境建议始终启用备份功能
4. **超时设置**: 根据网络环境和集群大小调整超时时间
5. **日志监控**: 定期检查清理日志，确保功能正常工作

## 相关文件

- `auto_cleanup_manager.py`: 自动清理管理器核心实现
- `cleanup_consumer_group.py`: 独立清理脚本
- `test_auto_cleanup.py`: 功能测试脚本
- `filter_config.yaml`: 配置文件（包含自动清理配置）
- `main.py`: 集成了自动清理功能的主程序
