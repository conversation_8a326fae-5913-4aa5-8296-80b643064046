#!/usr/bin/env python3
"""
测试describe_consumer_groups返回格式的脚本

专门用于调试和验证不同版本kafka-python库的describe_consumer_groups返回格式
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.config_manager import ConfigManager
from kafka_data_filter.kafka_consumer_group_manager import KafkaConsumerGroupManager


def setup_logging():
    """配置日志系统"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)


def test_describe_consumer_groups_format():
    """测试describe_consumer_groups的返回格式"""
    logger = setup_logging()
    
    print("🔍 测试 describe_consumer_groups 返回格式")
    print("=" * 70)
    
    try:
        # 加载配置
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        group_id = kafka_config.get('group_id', 'moye-check-data')
        
        print(f"Kafka服务器: {kafka_config['bootstrap_servers']}")
        print(f"目标消费者组: {group_id}")
        
        # 创建消费者组管理器
        group_manager = KafkaConsumerGroupManager(
            bootstrap_servers=kafka_config['bootstrap_servers']
        )
        
        print("\n1. 直接调用 describe_consumer_groups():")
        print("-" * 50)
        
        group_metadata_raw = group_manager.admin_client.describe_consumer_groups([group_id])
        
        print(f"返回类型: {type(group_metadata_raw)}")
        print(f"返回内容: {group_metadata_raw}")
        
        if hasattr(group_metadata_raw, '__len__'):
            print(f"返回长度: {len(group_metadata_raw)}")
        
        # 如果是字典
        if isinstance(group_metadata_raw, dict):
            print("✅ 返回格式为字典")
            print(f"字典键: {list(group_metadata_raw.keys())}")
            
            if group_id in group_metadata_raw:
                group_info = group_metadata_raw[group_id]
                print(f"目标消费者组信息类型: {type(group_info)}")
                print(f"目标消费者组信息: {group_info}")
                
                if hasattr(group_info, '__dict__'):
                    print(f"group_info属性: {group_info.__dict__}")
            else:
                print(f"❌ 字典中不包含消费者组 {group_id}")
        
        # 如果是列表
        elif isinstance(group_metadata_raw, list):
            print("✅ 返回格式为列表")
            
            for i, item in enumerate(group_metadata_raw):
                print(f"\n列表元素 {i}:")
                print(f"  类型: {type(item)}")
                print(f"  内容: {item}")
                
                if hasattr(item, '__dict__'):
                    print(f"  属性: {item.__dict__}")
                
                # 尝试提取group_id
                extracted_id = group_manager._extract_group_id_from_info(item)
                print(f"  提取的group_id: {extracted_id}")
        
        else:
            print(f"⚠️  未知返回格式: {type(group_metadata_raw)}")
        
        print("\n2. 测试解析方法:")
        print("-" * 50)
        
        group_metadata, group_info = group_manager._parse_describe_groups_result(
            group_metadata_raw, group_id
        )
        
        if group_metadata is not None and group_info is not None:
            print("✅ 解析成功")
            print(f"解析后的group_metadata: {group_metadata}")
            print(f"解析后的group_info: {group_info}")
            
            # 测试获取详细信息
            print("\n3. 测试获取详细信息:")
            print("-" * 50)
            
            detailed_info = group_manager.get_consumer_group_info(group_id)
            print(f"详细信息获取结果: {detailed_info}")
            
        else:
            print("❌ 解析失败")
            print(f"group_metadata: {group_metadata}")
            print(f"group_info: {group_info}")
        
        print("\n4. 测试备用检查方法:")
        print("-" * 50)
        
        exists, info = group_manager.consumer_group_exists_fallback(group_id)
        print(f"备用检查结果: exists={exists}, info={info}")
        
        group_manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_auto_cleanup_with_fixed_parsing():
    """测试修复后的自动清理功能"""
    logger = setup_logging()
    
    print("\n🧹 测试修复后的自动清理功能")
    print("=" * 70)
    
    try:
        # 加载配置
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        
        # 导入自动清理管理器
        from kafka_data_filter.auto_cleanup_manager import AutoCleanupManager
        
        # 创建自动清理管理器（禁用自动清理，手动测试）
        cleanup_manager = AutoCleanupManager(
            kafka_config=kafka_config,
            cleanup_on_exit=False,  # 禁用自动清理
            backup_before_cleanup=False
        )
        
        if not cleanup_manager.group_manager:
            print("❌ 消费者组管理器未初始化")
            return False
        
        group_id = kafka_config.get('group_id', 'moye-check-data')
        print(f"目标消费者组: {group_id}")
        
        print("\n1. 模拟自动清理检测流程:")
        print("-" * 50)
        
        # 等待状态稳定
        print("等待Kafka集群状态稳定...")
        import time
        time.sleep(2)
        
        # 方法1：使用轻量级检查
        print("执行轻量级检查...")
        exists, check_info = cleanup_manager.group_manager.consumer_group_exists(
            group_id, 
            retry_count=3, 
            retry_delay=1.0
        )
        
        print(f"轻量级检查结果: exists={exists}, info={check_info}")
        
        # 如果轻量级检查失败，尝试备用方法
        if not exists and 'error' in check_info:
            print("轻量级检查失败，尝试备用检查方法...")
            exists, check_info = cleanup_manager.group_manager.consumer_group_exists_fallback(group_id)
            print(f"备用检查结果: exists={exists}, info={check_info}")
        
        if exists:
            # 方法2：获取详细信息进行二次确认
            print("获取消费者组详细信息进行二次确认...")
            group_info = cleanup_manager.group_manager.get_consumer_group_info(
                group_id, 
                retry_count=2, 
                retry_delay=1.0
            )
            
            print(f"详细信息获取结果:")
            print(f"  存在: {group_info.get('exists', False)}")
            print(f"  状态: {group_info.get('state', 'Unknown')}")
            print(f"  成员数: {group_info.get('member_count', 0)}")
            print(f"  原因: {group_info.get('reason', 'N/A')}")
            print(f"  错误: {group_info.get('error', 'N/A')}")
            
            if group_info.get('exists', False):
                print("✅ 消费者组存在，自动清理检测成功")
                print("📝 在实际运行中，这里会继续执行删除操作")
            else:
                print("❌ 详细信息获取失败，但轻量级检查显示存在")
                print("📝 这可能表明仍有解析问题需要修复")
        else:
            print("❌ 消费者组不存在或检查失败")
        
        cleanup_manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 自动清理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 describe_consumer_groups 返回格式修复验证")
    print("=" * 80)
    
    tests = [
        ("describe_consumer_groups格式测试", test_describe_consumer_groups_format),
        ("修复后的自动清理测试", test_auto_cleanup_with_fixed_parsing)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔬 开始测试: {test_name}")
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！describe_consumer_groups解析修复成功！")
        print("\n📝 下一步:")
        print("1. 现在可以运行 python main.py filter_config.yaml 测试完整的自动清理")
        print("2. 消费者组应该能被正确检测和删除")
        print("3. 查看日志确认自动清理功能正常工作")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试执行失败: {e}")
        sys.exit(1)
