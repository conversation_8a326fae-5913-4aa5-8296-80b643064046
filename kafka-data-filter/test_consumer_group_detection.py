#!/usr/bin/env python3
"""
消费者组检测功能测试脚本

专门测试消费者组状态检测和自动清理功能的修复
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.config_manager import ConfigManager
from kafka_data_filter.kafka_consumer_group_manager import KafkaConsumerGroupManager
from kafka_data_filter.auto_cleanup_manager import AutoCleanupManager


def setup_logging():
    """配置日志系统"""
    logging.basicConfig(
        level=logging.DEBUG,  # 使用DEBUG级别查看详细信息
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('test_consumer_group_detection.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)


def test_consumer_group_detection():
    """测试消费者组检测功能"""
    logger = setup_logging()
    
    print("🔍 测试消费者组检测功能")
    print("=" * 60)
    
    try:
        # 加载配置
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        group_id = kafka_config.get('group_id', 'moye-check-data')
        
        print(f"目标消费者组: {group_id}")
        print(f"Kafka服务器: {kafka_config['bootstrap_servers']}")
        
        # 创建消费者组管理器
        group_manager = KafkaConsumerGroupManager(
            bootstrap_servers=kafka_config['bootstrap_servers']
        )
        
        print("\n1. 列出所有消费者组:")
        print("-" * 40)
        try:
            all_groups = group_manager.admin_client.list_consumer_groups()
            group_ids = [group.group_id for group in all_groups]
            print(f"发现 {len(group_ids)} 个消费者组:")
            for gid in group_ids:
                print(f"  - {gid}")
            
            if group_id in group_ids:
                print(f"✅ 目标消费者组 {group_id} 在列表中")
            else:
                print(f"❌ 目标消费者组 {group_id} 不在列表中")
                
        except Exception as e:
            print(f"❌ 列出消费者组失败: {e}")
        
        print("\n2. 使用轻量级检查方法:")
        print("-" * 40)
        try:
            exists, check_info = group_manager.consumer_group_exists(group_id)
            print(f"存在性检查结果: {exists}")
            print(f"检查信息: {check_info}")
            
        except Exception as e:
            print(f"❌ 轻量级检查失败: {e}")
        
        print("\n3. 使用详细信息获取方法:")
        print("-" * 40)
        try:
            group_info = group_manager.get_consumer_group_info(group_id)
            print(f"详细信息获取结果:")
            print(f"  存在: {group_info.get('exists', False)}")
            print(f"  状态: {group_info.get('state', 'Unknown')}")
            print(f"  成员数: {group_info.get('member_count', 0)}")
            print(f"  原因: {group_info.get('reason', 'N/A')}")
            print(f"  错误: {group_info.get('error', 'N/A')}")
            
        except Exception as e:
            print(f"❌ 详细信息获取失败: {e}")
        
        print("\n4. 测试重试机制:")
        print("-" * 40)
        try:
            print("使用3次重试，每次间隔1秒...")
            group_info = group_manager.get_consumer_group_info(
                group_id, 
                retry_count=3, 
                retry_delay=1.0
            )
            print(f"重试检查结果:")
            print(f"  存在: {group_info.get('exists', False)}")
            print(f"  尝试次数: {group_info.get('attempt', 'N/A')}")
            
        except Exception as e:
            print(f"❌ 重试机制测试失败: {e}")
        
        group_manager.close()
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False
    
    return True


def test_auto_cleanup_detection():
    """测试自动清理管理器的检测功能"""
    logger = setup_logging()
    
    print("\n🧹 测试自动清理检测功能")
    print("=" * 60)
    
    try:
        # 加载配置
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        group_id = kafka_config.get('group_id', 'moye-check-data')
        
        # 创建自动清理管理器（禁用自动清理）
        cleanup_manager = AutoCleanupManager(
            kafka_config=kafka_config,
            cleanup_on_exit=False,  # 禁用自动清理
            backup_before_cleanup=False
        )
        
        if not cleanup_manager.group_manager:
            print("❌ 消费者组管理器未初始化")
            return False
        
        print(f"目标消费者组: {group_id}")
        
        print("\n1. 模拟自动清理检测流程:")
        print("-" * 40)
        
        # 等待状态稳定
        print("等待Kafka集群状态稳定...")
        time.sleep(2)
        
        # 检查消费者组是否存在
        print("检查消费者组是否存在...")
        exists, check_info = cleanup_manager.group_manager.consumer_group_exists(
            group_id, 
            retry_count=3, 
            retry_delay=1.0
        )
        
        print(f"存在性检查结果: exists={exists}, info={check_info}")
        
        if exists:
            print("获取消费者组详细信息进行二次确认...")
            group_info = cleanup_manager.group_manager.get_consumer_group_info(
                group_id, 
                retry_count=2, 
                retry_delay=1.0
            )
            
            print(f"详细信息:")
            print(f"  存在: {group_info.get('exists', False)}")
            print(f"  状态: {group_info.get('state', 'Unknown')}")
            print(f"  成员数: {group_info.get('member_count', 0)}")
            
            if group_info.get('exists', False):
                print("✅ 消费者组存在，可以进行清理")
            else:
                print("❌ 二次确认显示消费者组不存在")
        else:
            print("❌ 消费者组不存在，无需清理")
        
        cleanup_manager.close()
        
    except Exception as e:
        print(f"❌ 自动清理检测测试失败: {e}")
        return False
    
    return True


def test_deletion_verification():
    """测试删除验证功能"""
    logger = setup_logging()
    
    print("\n✅ 测试删除验证功能")
    print("=" * 60)
    
    try:
        # 加载配置
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        group_id = kafka_config.get('group_id', 'moye-check-data')
        
        # 创建消费者组管理器
        group_manager = KafkaConsumerGroupManager(
            bootstrap_servers=kafka_config['bootstrap_servers']
        )
        
        print(f"测试消费者组 {group_id} 的删除验证功能")
        
        # 首先检查消费者组是否存在
        exists, check_info = group_manager.consumer_group_exists(group_id)
        print(f"当前状态: exists={exists}, info={check_info}")
        
        if exists:
            print("消费者组存在，测试删除验证逻辑...")
            # 模拟验证删除（不实际删除，只测试验证逻辑）
            print("模拟验证删除过程（5秒超时）...")
            
            # 这里我们知道消费者组存在，所以验证应该返回False（因为没有实际删除）
            result = group_manager.verify_group_deletion(group_id, max_wait_time=5)
            
            if not result:
                print("✅ 验证逻辑正常：消费者组未被删除，验证返回False")
            else:
                print("❌ 验证逻辑异常：消费者组存在但验证返回True")
        else:
            print("消费者组不存在，验证删除应该立即返回True...")
            result = group_manager.verify_group_deletion(group_id, max_wait_time=5)
            
            if result:
                print("✅ 验证逻辑正常：消费者组不存在，验证返回True")
            else:
                print("❌ 验证逻辑异常：消费者组不存在但验证返回False")
        
        group_manager.close()
        
    except Exception as e:
        print(f"❌ 删除验证测试失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    print("🧪 消费者组检测功能修复验证")
    print("=" * 80)
    
    tests = [
        ("消费者组检测功能", test_consumer_group_detection),
        ("自动清理检测功能", test_auto_cleanup_detection),
        ("删除验证功能", test_deletion_verification)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔬 开始测试: {test_name}")
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<25} {status}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！消费者组检测功能修复成功！")
        print("\n📝 建议:")
        print("1. 现在可以正常运行 python main.py filter_config.yaml")
        print("2. 程序退出时应该能正确检测并删除消费者组")
        print("3. 查看日志文件 test_consumer_group_detection.log 获取详细信息")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息和日志文件")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试执行失败: {e}")
        sys.exit(1)
