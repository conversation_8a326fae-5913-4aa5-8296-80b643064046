#!/usr/bin/env python3
"""
Kafka消费者组管理器

提供消费者组的创建、监控、停止和删除功能
包含安全检查和数据一致性保障
"""

import json
import time
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from kafka import KafkaConsumer, KafkaAdminClient
from kafka.admin import ConfigResource, ConfigResourceType
from kafka.structs import TopicPartition, OffsetAndMetadata
from kafka.errors import KafkaError, GroupCoordinatorNotAvailableError


class KafkaConsumerGroupManager:
    """Kafka消费者组管理器"""
    
    def __init__(self, bootstrap_servers: str, **kwargs):
        """
        初始化消费者组管理器
        
        参数:
            bootstrap_servers: Kafka集群地址
            **kwargs: 其他Kafka配置参数
        """
        self.bootstrap_servers = bootstrap_servers
        self.kafka_config = {
            'bootstrap_servers': bootstrap_servers,
            **kwargs
        }
        self.admin_client = None
        self.logger = logging.getLogger(__name__)
        
        # 初始化管理客户端
        self._init_admin_client()
    
    def _init_admin_client(self) -> None:
        """初始化Kafka管理客户端"""
        try:
            # 过滤出AdminClient支持的配置参数
            admin_config = self._filter_admin_config(self.kafka_config)
            self.admin_client = KafkaAdminClient(**admin_config)
            self.logger.info(f"成功连接到Kafka集群: {self.bootstrap_servers}")
        except Exception as e:
            self.logger.error(f"连接Kafka集群失败: {e}")
            raise RuntimeError(f"无法连接到Kafka集群: {e}")

    def _filter_admin_config(self, kafka_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        过滤出AdminClient支持的配置参数

        参数:
            kafka_config: 原始Kafka配置

        返回:
            AdminClient支持的配置字典
        """
        # AdminClient支持的配置参数列表
        admin_supported_configs = {
            'bootstrap_servers',
            'client_id',
            'request_timeout_ms',
            'connections_max_idle_ms',
            'retry_backoff_ms',
            'max_in_flight_requests_per_connection',
            'receive_buffer_bytes',
            'send_buffer_bytes',
            'socket_options',
            'sock_chunk_bytes',
            'sock_chunk_buffer_size',
            'reconnect_backoff_ms',
            'reconnect_backoff_max_ms',
            'max_in_flight_requests_per_connection',
            'security_protocol',
            'ssl_check_hostname',
            'ssl_context',
            'ssl_keyfile',
            'ssl_certfile',
            'ssl_cafile',
            'ssl_cadata',
            'ssl_ciphers',
            'ssl_password',
            'sasl_mechanism',
            'sasl_plain_username',
            'sasl_plain_password',
            'sasl_kerberos_service_name',
            'sasl_kerberos_domain_name',
            'sasl_oauth_token_provider',
            'api_version',
            'api_version_auto_timeout_ms',
            'metric_reporters',
            'metrics_num_samples',
            'metrics_sample_window_ms'
        }

        # 过滤配置
        admin_config = {}
        for key, value in kafka_config.items():
            if key in admin_supported_configs:
                admin_config[key] = value
            else:
                self.logger.debug(f"跳过AdminClient不支持的配置: {key}")

        return admin_config
    
    def get_consumer_group_info(self, group_id: str) -> Dict[str, Any]:
        """
        获取消费者组详细信息
        
        参数:
            group_id: 消费者组ID
            
        返回:
            消费者组信息字典
        """
        try:
            # 获取消费者组元数据
            group_metadata = self.admin_client.describe_consumer_groups([group_id])
            
            if group_id not in group_metadata:
                return {'exists': False, 'group_id': group_id}
            
            group_info = group_metadata[group_id]
            
            # 获取消费者组的offset信息
            offset_info = self._get_group_offsets(group_id)
            
            # 获取消费者组成员信息
            members_info = self._get_group_members(group_info)
            
            return {
                'exists': True,
                'group_id': group_id,
                'state': group_info.state,
                'protocol_type': group_info.protocol_type,
                'protocol': group_info.protocol,
                'members': members_info,
                'member_count': len(members_info),
                'coordinator': {
                    'id': group_info.coordinator.nodeId,
                    'host': group_info.coordinator.host,
                    'port': group_info.coordinator.port
                },
                'offsets': offset_info,
                'last_updated': datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"获取消费者组信息失败: {e}")
            return {'exists': False, 'error': str(e), 'group_id': group_id}
    
    def _get_group_offsets(self, group_id: str) -> Dict[str, Any]:
        """
        获取消费者组的offset信息

        参数:
            group_id: 消费者组ID

        返回:
            offset信息字典
        """
        try:
            # 创建临时消费者获取offset信息
            # 只使用消费者支持的配置参数
            consumer_config = {
                'group_id': group_id,
                'bootstrap_servers': self.bootstrap_servers,
                'enable_auto_commit': False,
                'auto_offset_reset': 'latest'
            }

            # 添加安全相关配置（如果存在）
            security_configs = ['security_protocol', 'sasl_mechanism',
                              'sasl_plain_username', 'sasl_plain_password']
            for config_key in security_configs:
                if config_key in self.kafka_config:
                    consumer_config[config_key] = self.kafka_config[config_key]

            temp_consumer = KafkaConsumer(**consumer_config)
            
            # 获取分配的分区
            partitions = temp_consumer.assignment()
            
            offset_info = {}
            for partition in partitions:
                try:
                    # 获取当前offset
                    current_offset = temp_consumer.position(partition)
                    
                    # 获取最新offset
                    end_offset = temp_consumer.end_offsets([partition])[partition]
                    
                    # 计算lag
                    lag = end_offset - current_offset if current_offset is not None else None
                    
                    offset_info[f"{partition.topic}-{partition.partition}"] = {
                        'topic': partition.topic,
                        'partition': partition.partition,
                        'current_offset': current_offset,
                        'end_offset': end_offset,
                        'lag': lag
                    }
                except Exception as e:
                    self.logger.warning(f"获取分区{partition}的offset失败: {e}")
            
            temp_consumer.close()
            return offset_info
            
        except Exception as e:
            self.logger.error(f"获取消费者组offset信息失败: {e}")
            return {}
    
    def _get_group_members(self, group_info) -> List[Dict[str, Any]]:
        """
        获取消费者组成员信息
        
        参数:
            group_info: 消费者组信息对象
            
        返回:
            成员信息列表
        """
        members = []
        for member in group_info.members:
            member_info = {
                'member_id': member.member_id,
                'client_id': member.client_id,
                'client_host': member.client_host,
                'member_metadata': self._parse_member_metadata(member.member_metadata),
                'member_assignment': self._parse_member_assignment(member.member_assignment)
            }
            members.append(member_info)
        
        return members
    
    def _parse_member_metadata(self, metadata: bytes) -> Dict[str, Any]:
        """解析成员元数据"""
        try:
            # 这里简化处理，实际可能需要更复杂的解析
            return {'raw_size': len(metadata) if metadata else 0}
        except Exception:
            return {'raw_size': 0}
    
    def _parse_member_assignment(self, assignment: bytes) -> Dict[str, Any]:
        """解析成员分配信息"""
        try:
            # 这里简化处理，实际可能需要更复杂的解析
            return {'raw_size': len(assignment) if assignment else 0}
        except Exception:
            return {'raw_size': 0}
    
    def check_group_safety_for_deletion(self, group_id: str) -> Tuple[bool, List[str]]:
        """
        检查消费者组是否可以安全删除
        
        参数:
            group_id: 消费者组ID
            
        返回:
            (是否安全, 警告信息列表)
        """
        warnings = []
        
        try:
            group_info = self.get_consumer_group_info(group_id)
            
            if not group_info.get('exists', False):
                return True, ['消费者组不存在']
            
            # 检查消费者组状态
            state = group_info.get('state', 'Unknown')
            if state not in ['Empty', 'Dead']:
                warnings.append(f"消费者组状态为 {state}，建议等待状态变为 Empty 或 Dead")
            
            # 检查活跃成员
            member_count = group_info.get('member_count', 0)
            if member_count > 0:
                warnings.append(f"消费者组仍有 {member_count} 个活跃成员")
            
            # 检查offset lag
            offsets = group_info.get('offsets', {})
            high_lag_partitions = []
            for partition_key, offset_data in offsets.items():
                lag = offset_data.get('lag')
                if lag is not None and lag > 1000:  # 可配置的阈值
                    high_lag_partitions.append(f"{partition_key}(lag: {lag})")
            
            if high_lag_partitions:
                warnings.append(f"以下分区存在较大lag: {', '.join(high_lag_partitions)}")
            
            # 判断是否安全
            is_safe = len(warnings) == 0 or (
                state in ['Empty', 'Dead'] and member_count == 0
            )
            
            return is_safe, warnings
            
        except Exception as e:
            self.logger.error(f"检查消费者组安全性失败: {e}")
            return False, [f"检查失败: {str(e)}"]
    
    def backup_consumer_group_offsets(self, group_id: str, backup_path: str = None) -> str:
        """
        备份消费者组的offset信息
        
        参数:
            group_id: 消费者组ID
            backup_path: 备份文件路径，默认自动生成
            
        返回:
            备份文件路径
        """
        if backup_path is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = f"consumer_group_backup_{group_id}_{timestamp}.json"
        
        try:
            group_info = self.get_consumer_group_info(group_id)
            
            backup_data = {
                'group_id': group_id,
                'backup_time': datetime.now().isoformat(),
                'group_info': group_info,
                'kafka_cluster': self.bootstrap_servers
            }
            
            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"消费者组 {group_id} 的信息已备份到: {backup_path}")
            return backup_path
            
        except Exception as e:
            self.logger.error(f"备份消费者组信息失败: {e}")
            raise RuntimeError(f"备份失败: {e}")

    def wait_for_group_empty(self, group_id: str, timeout_seconds: int = 300,
                           check_interval: int = 5) -> bool:
        """
        等待消费者组变为空状态

        参数:
            group_id: 消费者组ID
            timeout_seconds: 超时时间（秒）
            check_interval: 检查间隔（秒）

        返回:
            是否成功等待到空状态
        """
        start_time = time.time()

        self.logger.info(f"等待消费者组 {group_id} 变为空状态...")

        while time.time() - start_time < timeout_seconds:
            try:
                group_info = self.get_consumer_group_info(group_id)

                if not group_info.get('exists', False):
                    self.logger.info(f"消费者组 {group_id} 不存在")
                    return True

                state = group_info.get('state', 'Unknown')
                member_count = group_info.get('member_count', 0)

                if state in ['Empty', 'Dead'] and member_count == 0:
                    self.logger.info(f"消费者组 {group_id} 已变为空状态")
                    return True

                self.logger.info(f"消费者组 {group_id} 状态: {state}, 成员数: {member_count}")
                time.sleep(check_interval)

            except Exception as e:
                self.logger.warning(f"检查消费者组状态时出错: {e}")
                time.sleep(check_interval)

        self.logger.warning(f"等待消费者组 {group_id} 变为空状态超时")
        return False

    def delete_consumer_group(self, group_id: str, force: bool = False,
                            backup: bool = True) -> bool:
        """
        删除消费者组

        参数:
            group_id: 消费者组ID
            force: 是否强制删除（跳过安全检查）
            backup: 是否在删除前备份

        返回:
            是否删除成功
        """
        try:
            self.logger.info(f"开始删除消费者组: {group_id}")

            # 检查消费者组是否存在
            group_info = self.get_consumer_group_info(group_id)
            if not group_info.get('exists', False):
                self.logger.info(f"消费者组 {group_id} 不存在，无需删除")
                return True

            # 备份消费者组信息
            if backup:
                try:
                    backup_path = self.backup_consumer_group_offsets(group_id)
                    self.logger.info(f"已备份消费者组信息到: {backup_path}")
                except Exception as e:
                    self.logger.warning(f"备份失败，但继续删除操作: {e}")

            # 安全检查
            if not force:
                is_safe, warnings = self.check_group_safety_for_deletion(group_id)
                if not is_safe:
                    self.logger.error(f"消费者组 {group_id} 不满足安全删除条件:")
                    for warning in warnings:
                        self.logger.error(f"  - {warning}")
                    self.logger.error("使用 force=True 参数可强制删除")
                    return False

                if warnings:
                    self.logger.warning("删除前检查发现以下警告:")
                    for warning in warnings:
                        self.logger.warning(f"  - {warning}")

            # 执行删除操作
            self.logger.info(f"正在删除消费者组: {group_id}")

            # 使用Kafka Admin API删除消费者组
            delete_result = self.admin_client.delete_consumer_groups([group_id])

            # 检查删除结果
            if group_id in delete_result:
                future = delete_result[group_id]
                try:
                    future.result(timeout=30)  # 等待删除完成
                    self.logger.info(f"消费者组 {group_id} 删除成功")
                    return True
                except Exception as e:
                    self.logger.error(f"删除消费者组 {group_id} 失败: {e}")
                    return False
            else:
                self.logger.error(f"删除操作未返回消费者组 {group_id} 的结果")
                return False

        except Exception as e:
            self.logger.error(f"删除消费者组 {group_id} 时发生错误: {e}")
            return False

    def verify_group_deletion(self, group_id: str, max_wait_time: int = 60) -> bool:
        """
        验证消费者组是否已被完全删除

        参数:
            group_id: 消费者组ID
            max_wait_time: 最大等待时间（秒）

        返回:
            是否确认删除成功
        """
        start_time = time.time()

        self.logger.info(f"验证消费者组 {group_id} 是否已被删除...")

        while time.time() - start_time < max_wait_time:
            try:
                group_info = self.get_consumer_group_info(group_id)

                if not group_info.get('exists', False):
                    self.logger.info(f"确认消费者组 {group_id} 已被完全删除")
                    return True

                self.logger.info(f"消费者组 {group_id} 仍然存在，继续等待...")
                time.sleep(2)

            except Exception as e:
                self.logger.warning(f"验证删除状态时出错: {e}")
                time.sleep(2)

        self.logger.error(f"验证超时，无法确认消费者组 {group_id} 是否已被删除")
        return False

    def list_all_consumer_groups(self) -> List[Dict[str, Any]]:
        """
        列出所有消费者组

        返回:
            消费者组信息列表
        """
        try:
            groups_metadata = self.admin_client.list_consumer_groups()

            groups_info = []
            for group in groups_metadata:
                group_detail = self.get_consumer_group_info(group.group_id)
                groups_info.append(group_detail)

            return groups_info

        except Exception as e:
            self.logger.error(f"列出消费者组失败: {e}")
            return []

    def close(self) -> None:
        """关闭管理客户端"""
        if self.admin_client:
            try:
                self.admin_client.close()
                self.logger.info("Kafka管理客户端已关闭")
            except Exception as e:
                self.logger.error(f"关闭Kafka管理客户端失败: {e}")
            finally:
                self.admin_client = None
