#!/usr/bin/env python3
"""
Kafka消费者组自动清理管理器

在程序关闭时自动删除消费者组，避免在Kafka集群中留下无用的消费者组记录
支持正常关闭和异常退出时的自动清理
"""

import os
import sys
import time
import atexit
import signal
import logging
import threading
from typing import Dict, Any, Optional, Callable
from datetime import datetime

from .kafka_consumer_group_manager import KafkaConsumerGroupManager


class AutoCleanupManager:
    """自动清理管理器"""
    
    def __init__(self, kafka_config: Dict[str, Any], 
                 cleanup_on_exit: bool = True,
                 cleanup_timeout: int = 30,
                 backup_before_cleanup: bool = True):
        """
        初始化自动清理管理器
        
        参数:
            kafka_config: Kafka配置字典
            cleanup_on_exit: 是否在程序退出时自动清理
            cleanup_timeout: 清理操作超时时间（秒）
            backup_before_cleanup: 清理前是否备份
        """
        self.kafka_config = kafka_config
        self.group_id = kafka_config.get('group_id', 'kafka-data-filter')
        self.cleanup_on_exit = cleanup_on_exit
        self.cleanup_timeout = cleanup_timeout
        self.backup_before_cleanup = backup_before_cleanup
        
        self.logger = logging.getLogger(__name__)
        self.group_manager = None
        self.cleanup_executed = False
        self.cleanup_lock = threading.Lock()
        
        # 初始化消费者组管理器
        self._init_group_manager()
        
        # 注册清理处理器
        if cleanup_on_exit:
            self._register_cleanup_handlers()
        
        self.logger.info(f"自动清理管理器已初始化，目标消费者组: {self.group_id}")
    
    def _init_group_manager(self) -> None:
        """初始化消费者组管理器"""
        try:
            bootstrap_servers = self.kafka_config['bootstrap_servers']
            other_config = {k: v for k, v in self.kafka_config.items() 
                          if k not in ['bootstrap_servers', 'group_id', 'topic']}
            
            self.group_manager = KafkaConsumerGroupManager(
                bootstrap_servers=bootstrap_servers,
                **other_config
            )
            
            self.logger.info("消费者组管理器初始化成功")
            
        except Exception as e:
            self.logger.error(f"初始化消费者组管理器失败: {e}")
            # 不抛出异常，避免影响主程序启动
    
    def _register_cleanup_handlers(self) -> None:
        """注册清理处理器"""
        try:
            # 注册atexit处理器（正常退出）
            atexit.register(self._cleanup_on_exit)
            
            # 注册信号处理器（异常退出）
            signal.signal(signal.SIGTERM, self._cleanup_on_signal)
            signal.signal(signal.SIGINT, self._cleanup_on_signal)
            
            # 在Windows上注册SIGBREAK
            if hasattr(signal, 'SIGBREAK'):
                signal.signal(signal.SIGBREAK, self._cleanup_on_signal)
            
            self.logger.info("清理处理器注册成功")
            
        except Exception as e:
            self.logger.error(f"注册清理处理器失败: {e}")
    
    def _cleanup_on_exit(self) -> None:
        """程序正常退出时的清理处理器"""
        try:
            self.logger.info("程序正常退出，执行自动清理...")
            self._execute_cleanup(reason="正常退出")
        except Exception as e:
            self.logger.error(f"退出清理失败: {e}")
    
    def _cleanup_on_signal(self, signum: int, frame) -> None:
        """信号处理器"""
        try:
            signal_name = signal.Signals(signum).name
            self.logger.info(f"收到信号 {signal_name}，执行自动清理...")
            self._execute_cleanup(reason=f"信号退出({signal_name})")
        except Exception as e:
            self.logger.error(f"信号清理失败: {e}")
        finally:
            # 恢复默认信号处理并重新发送信号
            signal.signal(signum, signal.SIG_DFL)
            os.kill(os.getpid(), signum)
    
    def _execute_cleanup(self, reason: str = "未知原因") -> None:
        """
        执行清理操作
        
        参数:
            reason: 清理原因
        """
        with self.cleanup_lock:
            if self.cleanup_executed:
                self.logger.info("清理操作已执行，跳过重复执行")
                return
            
            self.cleanup_executed = True
        
        try:
            self.logger.info(f"开始执行消费者组清理操作，原因: {reason}")
            
            if not self.group_manager:
                self.logger.warning("消费者组管理器未初始化，跳过清理")
                return
            
            # 检查消费者组是否存在
            group_info = self.group_manager.get_consumer_group_info(self.group_id)
            if not group_info.get('exists', False):
                self.logger.info(f"消费者组 {self.group_id} 不存在，无需清理")
                return
            
            # 等待消费者组变为空状态（短时间等待）
            self.logger.info("等待消费者组变为空状态...")
            empty_timeout = min(10, self.cleanup_timeout // 3)  # 最多等待10秒或总超时的1/3
            self.group_manager.wait_for_group_empty(
                group_id=self.group_id,
                timeout_seconds=empty_timeout,
                check_interval=1
            )
            
            # 备份消费者组信息（如果启用）
            if self.backup_before_cleanup:
                try:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_path = f"auto_cleanup_backup_{self.group_id}_{timestamp}.json"
                    self.group_manager.backup_consumer_group_offsets(
                        group_id=self.group_id,
                        backup_path=backup_path
                    )
                    self.logger.info(f"消费者组信息已备份到: {backup_path}")
                except Exception as e:
                    self.logger.warning(f"备份消费者组信息失败: {e}")
            
            # 执行删除操作
            self.logger.info(f"删除消费者组: {self.group_id}")
            success = self.group_manager.delete_consumer_group(
                group_id=self.group_id,
                force=True,  # 自动清理时使用强制删除
                backup=False  # 已经备份过了
            )
            
            if success:
                self.logger.info(f"✅ 消费者组 {self.group_id} 自动清理成功")
                
                # 验证删除结果（短时间验证）
                verify_timeout = min(5, self.cleanup_timeout // 6)  # 最多验证5秒
                if self.group_manager.verify_group_deletion(self.group_id, verify_timeout):
                    self.logger.info("✅ 删除验证通过")
                else:
                    self.logger.warning("⚠️ 删除验证超时，但删除操作可能已成功")
            else:
                self.logger.error(f"❌ 消费者组 {self.group_id} 自动清理失败")
            
        except Exception as e:
            self.logger.error(f"执行清理操作时发生错误: {e}")
            # 不抛出异常，避免影响程序正常退出
        finally:
            # 关闭管理器
            try:
                if self.group_manager:
                    self.group_manager.close()
            except Exception as e:
                self.logger.error(f"关闭消费者组管理器失败: {e}")
    
    def manual_cleanup(self, force: bool = False) -> bool:
        """
        手动执行清理操作
        
        参数:
            force: 是否强制清理
            
        返回:
            是否清理成功
        """
        try:
            self.logger.info("执行手动清理操作...")
            
            if not self.group_manager:
                self.logger.error("消费者组管理器未初始化")
                return False
            
            # 检查消费者组是否存在
            group_info = self.group_manager.get_consumer_group_info(self.group_id)
            if not group_info.get('exists', False):
                self.logger.info(f"消费者组 {self.group_id} 不存在")
                return True
            
            # 安全检查（除非强制）
            if not force:
                is_safe, warnings = self.group_manager.check_group_safety_for_deletion(self.group_id)
                if not is_safe:
                    self.logger.error("消费者组不满足安全删除条件:")
                    for warning in warnings:
                        self.logger.error(f"  - {warning}")
                    return False
            
            # 备份并删除
            if self.backup_before_cleanup:
                try:
                    backup_path = self.group_manager.backup_consumer_group_offsets(self.group_id)
                    self.logger.info(f"消费者组信息已备份到: {backup_path}")
                except Exception as e:
                    self.logger.warning(f"备份失败: {e}")
                    if not force:
                        return False
            
            # 执行删除
            success = self.group_manager.delete_consumer_group(
                group_id=self.group_id,
                force=force,
                backup=False
            )
            
            if success:
                self.logger.info(f"✅ 消费者组 {self.group_id} 手动清理成功")
                
                # 验证删除结果
                if self.group_manager.verify_group_deletion(self.group_id):
                    self.logger.info("✅ 删除验证通过")
                    return True
                else:
                    self.logger.warning("⚠️ 删除验证失败")
                    return False
            else:
                self.logger.error(f"❌ 消费者组 {self.group_id} 手动清理失败")
                return False
                
        except Exception as e:
            self.logger.error(f"手动清理操作失败: {e}")
            return False
    
    def disable_auto_cleanup(self) -> None:
        """禁用自动清理"""
        self.cleanup_on_exit = False
        self.logger.info("自动清理已禁用")
    
    def enable_auto_cleanup(self) -> None:
        """启用自动清理"""
        self.cleanup_on_exit = True
        if not hasattr(self, '_handlers_registered'):
            self._register_cleanup_handlers()
        self.logger.info("自动清理已启用")
    
    def get_cleanup_status(self) -> Dict[str, Any]:
        """
        获取清理状态信息
        
        返回:
            状态信息字典
        """
        return {
            'group_id': self.group_id,
            'cleanup_on_exit': self.cleanup_on_exit,
            'cleanup_executed': self.cleanup_executed,
            'backup_before_cleanup': self.backup_before_cleanup,
            'cleanup_timeout': self.cleanup_timeout,
            'manager_initialized': self.group_manager is not None
        }
    
    def close(self) -> None:
        """关闭自动清理管理器"""
        try:
            if self.group_manager:
                self.group_manager.close()
                self.group_manager = None
            self.logger.info("自动清理管理器已关闭")
        except Exception as e:
            self.logger.error(f"关闭自动清理管理器失败: {e}")
