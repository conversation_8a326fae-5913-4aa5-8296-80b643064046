"""
配置管理模块

负责读取和验证YAML配置文件，提供配置参数访问接口
"""

import yaml
import os
from typing import Dict, Any, List, Optional
import logging


class ConfigManager:
    """配置管理器类，负责加载和验证配置文件"""
    
    def __init__(self, config_path: str):
        """
        初始化配置管理器
        
        参数:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config = {}
        self.logger = logging.getLogger(__name__)
        self._load_config()
        self._validate_config()
    
    def _load_config(self) -> None:
        """从YAML文件加载配置"""
        try:
            if not os.path.exists(self.config_path):
                raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
            
            with open(self.config_path, 'r', encoding='utf-8') as file:
                self.config = yaml.safe_load(file)
                
            self.logger.info(f"成功加载配置文件: {self.config_path}")
            
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
        except Exception as e:
            raise RuntimeError(f"加载配置文件失败: {e}")
    
    def _validate_config(self) -> None:
        """验证配置文件的必要字段"""
        required_sections = ['kafka', 'filter', 'output']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"配置文件缺少必要部分: {section}")
        
        # 验证Kafka配置
        kafka_config = self.config['kafka']
        required_kafka_fields = ['bootstrap_servers', 'topic']
        for field in required_kafka_fields:
            if field not in kafka_config:
                raise ValueError(f"Kafka配置缺少必要字段: {field}")
        
        # 验证过滤配置
        filter_config = self.config['filter']
        if 'rules' not in filter_config:
            raise ValueError("过滤配置缺少rules字段")
        
        # 验证输出配置
        output_config = self.config['output']
        if 'type' not in output_config:
            raise ValueError("输出配置缺少type字段")
        
        self.logger.info("配置文件验证通过")
    
    def get_kafka_config(self) -> Dict[str, Any]:
        """
        获取Kafka配置
        
        返回:
            Kafka配置字典
        """
        return self.config['kafka']
    
    def get_filter_rules(self) -> List[str]:
        """
        获取过滤规则列表
        
        返回:
            过滤规则字符串列表
        """
        return self.config['filter']['rules']
    
    def get_output_config(self) -> Dict[str, Any]:
        """
        获取输出配置
        
        返回:
            输出配置字典
        """
        return self.config['output']
    
    def get_consumer_config(self) -> Dict[str, Any]:
        """
        获取消费者配置
        
        返回:
            消费者配置字典
        """
        kafka_config = self.get_kafka_config()
        consumer_config = {
            'bootstrap_servers': kafka_config['bootstrap_servers'],
            'group_id': kafka_config.get('group_id', 'kafka-data-filter'),
            'auto_offset_reset': kafka_config.get('auto_offset_reset', 'latest'),
            'enable_auto_commit': kafka_config.get('enable_auto_commit', True),
            'value_deserializer': lambda x: x.decode('utf-8') if x else None
        }
        
        # 添加其他可选配置
        optional_fields = ['security_protocol', 'sasl_mechanism', 'sasl_username', 'sasl_password']
        for field in optional_fields:
            if field in kafka_config:
                consumer_config[field] = kafka_config[field]
        
        return consumer_config
    
    def get_topic(self) -> str:
        """
        获取要消费的topic名称
        
        返回:
            topic名称
        """
        return self.config['kafka']['topic']
    
    def get_log_level(self) -> str:
        """
        获取日志级别

        返回:
            日志级别字符串
        """
        return self.config.get('logging', {}).get('level', 'INFO')

    def get_auto_cleanup_config(self) -> Dict[str, Any]:
        """
        获取自动清理配置

        返回:
            自动清理配置字典
        """
        default_config = {
            'enabled': True,
            'timeout': 30,
            'backup_before_cleanup': True,
            'cleanup_on_signal': True
        }

        auto_cleanup_config = self.config.get('auto_cleanup', {})

        # 合并默认配置和用户配置
        result = default_config.copy()
        result.update(auto_cleanup_config)

        return result
    
    def get_statistics_config(self) -> Dict[str, Any]:
        """
        获取统计配置
        
        返回:
            统计配置字典
        """
        return self.config.get('statistics', {
            'enabled': True,
            'interval': 10,
            'show_details': True
        })
    
    def reload_config(self) -> None:
        """重新加载配置文件"""
        self._load_config()
        self._validate_config()
        self.logger.info("配置文件已重新加载")
