"""
Kafka数据过滤程序主入口

集成所有模块，提供完整的Kafka数据过滤功能
"""

import logging
import signal
import sys
import time
import os
from typing import Optional, Dict, Any

# 添加当前目录到Python路径，支持直接运行
if __name__ == "__main__":
    # 获取当前文件的目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 获取项目根目录
    project_root = os.path.dirname(current_dir)
    # 添加到Python路径
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

# 尝试相对导入，如果失败则使用绝对导入
try:
    from .config_manager import ConfigManager
    from .kafka_consumer import KafkaMessageConsumer
    from .data_filter import DataFilter
    from .output_handler import OutputHandler
    from .statistics import Statistics
except ImportError:
    # 直接运行时使用绝对导入
    from kafka_data_filter.config_manager import ConfigManager
    from kafka_data_filter.kafka_consumer import KafkaMessageConsumer
    from kafka_data_filter.data_filter import DataFilter
    from kafka_data_filter.output_handler import OutputHandler
    from kafka_data_filter.statistics import Statistics


class KafkaDataFilterApp:
    """Kafka数据过滤应用程序主类"""
    
    def __init__(self, config_path: str):
        """
        初始化应用程序
        
        参数:
            config_path: 配置文件路径
        """
        self.config_path = config_path
        self.config_manager = None
        self.kafka_consumer = None
        self.data_filter = None
        self.output_handler = None
        self.statistics = None
        self.logger = None
        self.is_running = False
        
        # 初始化组件
        self._init_components()
        
        # 注册信号处理器
        self._register_signal_handlers()
    
    def _init_components(self) -> None:
        """初始化所有组件"""
        try:
            # 初始化配置管理器
            self.config_manager = ConfigManager(self.config_path)
            
            # 配置日志
            self._setup_logging()
            
            self.logger.info("开始初始化Kafka数据过滤程序")
            
            # 初始化数据过滤器
            filter_rules = self.config_manager.get_filter_rules()
            self.data_filter = DataFilter(filter_rules)
            self.logger.info(f"数据过滤器已初始化，规则数量: {len(filter_rules)}")
            
            # 初始化输出处理器
            output_config = self.config_manager.get_output_config()
            self.output_handler = OutputHandler(output_config)
            self.logger.info("输出处理器已初始化")
            
            # 初始化统计模块
            stats_config = self.config_manager.get_statistics_config()
            self.statistics = Statistics(stats_config)
            self.logger.info("统计模块已初始化")
            
            # 初始化Kafka消费者
            consumer_config = self.config_manager.get_consumer_config()
            self.kafka_consumer = KafkaMessageConsumer(consumer_config)
            self.logger.info("Kafka消费者已初始化")
            
            self.logger.info("所有组件初始化完成")
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"组件初始化失败: {e}")
            else:
                print(f"组件初始化失败: {e}")
            raise
    
    def _setup_logging(self) -> None:
        """配置日志系统"""
        log_level = self.config_manager.get_log_level()
        
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(sys.stdout),
                logging.FileHandler('kafka_data_filter.log', encoding='utf-8')
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"日志系统已配置，级别: {log_level}")
    
    def _register_signal_handlers(self) -> None:
        """注册信号处理器"""
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        if hasattr(signal, 'SIGHUP'):
            signal.signal(signal.SIGHUP, self._signal_handler)
    
    def _signal_handler(self, signum: int, frame) -> None:
        """
        信号处理器
        
        参数:
            signum: 信号编号
            frame: 当前栈帧
        """
        signal_names = {
            signal.SIGINT: 'SIGINT',
            signal.SIGTERM: 'SIGTERM'
        }
        
        if hasattr(signal, 'SIGHUP'):
            signal_names[signal.SIGHUP] = 'SIGHUP'
        
        signal_name = signal_names.get(signum, f'Signal {signum}')
        self.logger.info(f"收到信号 {signal_name}，准备停止程序")
        
        self.stop()
    
    def _message_handler(self, message_data: dict) -> None:
        """
        消息处理函数
        
        参数:
            message_data: 消息数据
        """
        try:
            start_time = time.time()
            
            # 记录消费统计
            self.statistics.record_consumed()
            
            # 执行过滤
            passed, filter_details = self.data_filter.filter_message(message_data)
            
            # 记录处理统计
            processing_time = time.time() - start_time
            self.statistics.record_processed(processing_time=processing_time)
            
            if passed:
                # 记录通过统计
                self.statistics.record_passed()
                
                # 输出消息
                self.output_handler.output_message(message_data, filter_details)
                
                # 记录输出统计
                self.statistics.record_output()
            
            # 定期输出统计信息
            if self.statistics.should_report():
                self.statistics.print_report()
            
        except Exception as e:
            self.logger.error(f"处理消息失败: {e}")
            self.statistics.record_error()
    
    def run(self, max_messages: Optional[int] = None) -> None:
        """
        运行应用程序
        
        参数:
            max_messages: 最大处理消息数，None表示无限制
        """
        try:
            self.logger.info("启动Kafka数据过滤程序")
            self.is_running = True
            
            # 启动统计
            self.statistics.start()
            
            # 连接Kafka并开始消费
            topic = self.config_manager.get_topic()
            self.kafka_consumer.connect(topic)
            
            self.logger.info(f"开始消费topic: {topic}")
            
            # 消费消息
            self.kafka_consumer.consume_messages(
                message_handler=self._message_handler,
                max_messages=max_messages
            )
            
        except KeyboardInterrupt:
            self.logger.info("用户中断程序")
        except Exception as e:
            self.logger.error(f"程序运行失败: {e}")
            raise
        finally:
            self._cleanup()
    
    def stop(self) -> None:
        """停止应用程序"""
        self.logger.info("正在停止应用程序...")
        self.is_running = False

        if self.kafka_consumer:
            self.kafka_consumer.stop()

    def graceful_shutdown(self, timeout_seconds: int = 30) -> bool:
        """
        优雅关闭应用程序

        参数:
            timeout_seconds: 关闭超时时间（秒）

        返回:
            是否成功关闭
        """
        try:
            self.logger.info("开始优雅关闭应用程序...")
            self.is_running = False

            # 优雅关闭Kafka消费者
            if self.kafka_consumer:
                success = self.kafka_consumer.graceful_shutdown(timeout_seconds)
                if not success:
                    self.logger.warning("Kafka消费者优雅关闭失败，将强制关闭")

            # 输出最终统计信息
            if self.statistics:
                self.logger.info("输出最终统计信息...")
                final_stats = self.statistics.get_current_statistics()
                self.logger.info(self.statistics.generate_report())

            self.logger.info("应用程序优雅关闭完成")
            return True

        except Exception as e:
            self.logger.error(f"优雅关闭失败: {e}")
            return False
    
    def _cleanup(self) -> None:
        """清理资源"""
        self.logger.info("开始清理资源")
        
        try:
            # 输出最终统计信息
            if self.statistics:
                final_stats = self.statistics.get_current_statistics()
                self.logger.info("最终统计信息:")
                self.logger.info(self.statistics.generate_report())
                
                # 输出到输出处理器
                if self.output_handler:
                    filter_stats = self.data_filter.get_statistics() if self.data_filter else {}
                    combined_stats = {**final_stats, **filter_stats}
                    self.output_handler.output_statistics(combined_stats)
            
            # 关闭Kafka消费者
            if self.kafka_consumer:
                # 尝试优雅关闭，如果失败则强制关闭
                if not self.kafka_consumer.graceful_shutdown(timeout_seconds=10):
                    self.logger.warning("优雅关闭失败，执行强制关闭")
                    self.kafka_consumer.close()
                self.logger.info("Kafka消费者已关闭")
            
            # 关闭输出处理器
            if self.output_handler:
                self.output_handler.close()
                self.logger.info("输出处理器已关闭")
            
            self.logger.info("资源清理完成")
            
        except Exception as e:
            self.logger.error(f"清理资源时发生错误: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """
        获取应用程序状态
        
        返回:
            状态信息字典
        """
        status = {
            'is_running': self.is_running,
            'config_path': self.config_path
        }
        
        if self.statistics:
            status['statistics'] = self.statistics.get_current_statistics()
        
        if self.data_filter:
            status['filter_statistics'] = self.data_filter.get_statistics()
        
        if self.kafka_consumer:
            status['kafka_statistics'] = self.kafka_consumer.get_statistics()
        
        if self.output_handler:
            status['output_count'] = self.output_handler.get_output_count()
        
        return status
    
    def reload_config(self) -> None:
        """重新加载配置"""
        try:
            self.logger.info("重新加载配置")
            
            # 重新加载配置管理器
            self.config_manager.reload_config()
            
            # 重新初始化数据过滤器
            filter_rules = self.config_manager.get_filter_rules()
            self.data_filter = DataFilter(filter_rules)
            
            self.logger.info("配置重新加载完成")
            
        except Exception as e:
            self.logger.error(f"重新加载配置失败: {e}")
            raise


def main(config_path: str, max_messages: Optional[int] = None) -> None:
    """
    主函数

    参数:
        config_path: 配置文件路径
        max_messages: 最大处理消息数
    """
    try:
        app = KafkaDataFilterApp(config_path)
        app.run(max_messages)
    except Exception as e:
        print(f"程序运行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    import argparse
    import os

    parser = argparse.ArgumentParser(
        description="Kafka数据过滤程序",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python kafka_data_filter/main.py filter_config.yaml
  python kafka_data_filter/main.py filter_config.yaml --max-messages 1000
  python kafka_data_filter/main.py example_config.yaml --max-messages 500 --verbose
        """
    )

    parser.add_argument(
        "config",
        help="配置文件路径"
    )

    parser.add_argument(
        "--max-messages",
        type=int,
        help="最大处理消息数量（默认无限制）"
    )

    parser.add_argument(
        "--verbose", "-v",
        action="store_true",
        help="启用详细输出"
    )

    parser.add_argument(
        "--version",
        action="version",
        version="Kafka数据过滤程序 v1.0.0"
    )

    args = parser.parse_args()

    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        sys.exit(1)

    try:
        print("=" * 60)
        print("Kafka数据过滤程序")
        print("=" * 60)
        print(f"配置文件: {args.config}")
        print(f"最大消息数: {args.max_messages or '无限制'}")
        print(f"详细模式: {'启用' if args.verbose else '禁用'}")
        print("=" * 60)
        print("按 Ctrl+C 停止程序")
        print("=" * 60)

        # 运行程序
        main(args.config, args.max_messages)

    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)
