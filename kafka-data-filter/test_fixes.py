#!/usr/bin/env python3
"""
修复验证测试脚本

验证Kafka配置和自动清理功能的修复是否成功
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.config_manager import ConfigManager
from kafka_data_filter.kafka_consumer_group_manager import KafkaConsumerGroupManager
from kafka_data_filter.auto_cleanup_manager import AutoCleanupManager
from kafka_data_filter.main import KafkaDataFilterApp


def setup_logging():
    """配置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)


def test_config_loading():
    """测试配置加载"""
    logger = setup_logging()
    
    print("🔧 测试1: 配置文件加载")
    print("=" * 50)
    
    try:
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        auto_cleanup_config = config_manager.get_auto_cleanup_config()
        
        print("✅ 配置文件加载成功")
        print(f"Kafka配置: {kafka_config}")
        print(f"自动清理配置: {auto_cleanup_config}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置文件加载失败: {e}")
        return False


def test_consumer_group_manager():
    """测试消费者组管理器初始化"""
    logger = setup_logging()
    
    print("\n🔧 测试2: 消费者组管理器初始化")
    print("=" * 50)
    
    try:
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        
        # 测试消费者组管理器初始化
        group_manager = KafkaConsumerGroupManager(
            bootstrap_servers=kafka_config['bootstrap_servers']
        )
        
        print("✅ 消费者组管理器初始化成功")
        
        # 测试获取消费者组信息
        group_id = kafka_config.get('group_id', 'test-group')
        group_info = group_manager.get_consumer_group_info(group_id)
        
        print(f"消费者组 {group_id} 信息: {group_info}")
        
        group_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ 消费者组管理器测试失败: {e}")
        return False


def test_auto_cleanup_manager():
    """测试自动清理管理器初始化"""
    logger = setup_logging()
    
    print("\n🔧 测试3: 自动清理管理器初始化")
    print("=" * 50)
    
    try:
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        
        # 测试自动清理管理器初始化
        cleanup_manager = AutoCleanupManager(
            kafka_config=kafka_config,
            cleanup_on_exit=False  # 测试时禁用自动清理
        )
        
        print("✅ 自动清理管理器初始化成功")
        
        # 获取清理状态
        status = cleanup_manager.get_cleanup_status()
        print(f"清理状态: {status}")
        
        cleanup_manager.close()
        return True
        
    except Exception as e:
        print(f"❌ 自动清理管理器测试失败: {e}")
        return False


def test_main_app_initialization():
    """测试主应用程序初始化"""
    logger = setup_logging()
    
    print("\n🔧 测试4: 主应用程序初始化")
    print("=" * 50)
    
    try:
        # 测试主应用程序初始化
        app = KafkaDataFilterApp("filter_config.yaml")
        
        print("✅ 主应用程序初始化成功")
        
        # 获取清理状态
        cleanup_status = app.get_cleanup_status()
        print(f"应用程序清理状态: {cleanup_status}")
        
        # 测试手动清理功能（不实际执行）
        if app.auto_cleanup_manager:
            print("✅ 自动清理管理器已正确集成")
        else:
            print("⚠️  自动清理管理器未初始化")
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用程序初始化失败: {e}")
        return False


def test_graceful_shutdown():
    """测试优雅关闭功能"""
    logger = setup_logging()
    
    print("\n🔧 测试5: 优雅关闭功能")
    print("=" * 50)
    
    try:
        # 创建应用程序实例
        app = KafkaDataFilterApp("filter_config.yaml")
        
        print("应用程序已启动，测试优雅关闭...")
        
        # 测试优雅关闭
        success = app.graceful_shutdown(timeout_seconds=10)
        
        if success:
            print("✅ 优雅关闭测试成功")
        else:
            print("⚠️  优雅关闭测试部分成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 优雅关闭测试失败: {e}")
        return False


def run_all_tests():
    """运行所有测试"""
    print("🧪 Kafka配置和自动清理功能修复验证")
    print("=" * 60)
    
    tests = [
        ("配置文件加载", test_config_loading),
        ("消费者组管理器", test_consumer_group_manager),
        ("自动清理管理器", test_auto_cleanup_manager),
        ("主应用程序初始化", test_main_app_initialization),
        ("优雅关闭功能", test_graceful_shutdown)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 60)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！修复成功！")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        return False


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="修复验证测试工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
测试选项:
  config      - 测试配置文件加载
  manager     - 测试消费者组管理器
  cleanup     - 测试自动清理管理器
  app         - 测试主应用程序
  shutdown    - 测试优雅关闭
  all         - 运行所有测试（默认）
        """
    )
    
    parser.add_argument(
        "test",
        choices=["config", "manager", "cleanup", "app", "shutdown", "all"],
        nargs="?",
        default="all",
        help="要运行的测试（默认: all）"
    )
    
    args = parser.parse_args()
    
    try:
        if args.test == "config":
            success = test_config_loading()
        elif args.test == "manager":
            success = test_consumer_group_manager()
        elif args.test == "cleanup":
            success = test_auto_cleanup_manager()
        elif args.test == "app":
            success = test_main_app_initialization()
        elif args.test == "shutdown":
            success = test_graceful_shutdown()
        else:  # all
            success = run_all_tests()
        
        if success:
            print("\n✅ 测试完成，修复验证成功！")
            sys.exit(0)
        else:
            print("\n❌ 测试失败，请检查错误信息")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
