#!/usr/bin/env python3
"""
自动清理功能测试脚本

测试程序在正常退出和异常退出时的自动清理功能
"""

import sys
import time
import signal
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.main import KafkaDataFilterApp


def test_normal_exit():
    """测试正常退出时的自动清理"""
    print("=" * 60)
    print("测试1: 正常退出时的自动清理")
    print("=" * 60)
    
    try:
        # 创建应用程序实例
        app = KafkaDataFilterApp("filter_config.yaml")
        
        print("应用程序已启动，将在5秒后正常退出...")
        time.sleep(5)
        
        # 正常停止
        app.stop()
        print("应用程序已正常停止")
        
        # 检查清理状态
        cleanup_status = app.get_cleanup_status()
        print(f"清理状态: {cleanup_status}")
        
    except Exception as e:
        print(f"测试失败: {e}")


def test_signal_exit():
    """测试信号退出时的自动清理"""
    print("\n" + "=" * 60)
    print("测试2: 信号退出时的自动清理")
    print("=" * 60)
    
    try:
        # 创建应用程序实例
        app = KafkaDataFilterApp("filter_config.yaml")
        
        print("应用程序已启动，将在3秒后发送SIGTERM信号...")
        time.sleep(3)
        
        # 发送SIGTERM信号
        import os
        os.kill(os.getpid(), signal.SIGTERM)
        
    except Exception as e:
        print(f"测试过程中发生异常: {e}")


def test_manual_cleanup():
    """测试手动清理功能"""
    print("\n" + "=" * 60)
    print("测试3: 手动清理功能")
    print("=" * 60)
    
    try:
        # 创建应用程序实例
        app = KafkaDataFilterApp("filter_config.yaml")
        
        print("应用程序已启动，执行手动清理...")
        
        # 执行手动清理
        success = app.manual_cleanup_consumer_group(force=True)
        
        if success:
            print("✅ 手动清理成功")
        else:
            print("❌ 手动清理失败")
        
        # 检查清理状态
        cleanup_status = app.get_cleanup_status()
        print(f"清理状态: {cleanup_status}")
        
    except Exception as e:
        print(f"测试失败: {e}")


def test_cleanup_status():
    """测试清理状态查询"""
    print("\n" + "=" * 60)
    print("测试4: 清理状态查询")
    print("=" * 60)
    
    try:
        # 创建应用程序实例
        app = KafkaDataFilterApp("filter_config.yaml")
        
        # 获取清理状态
        cleanup_status = app.get_cleanup_status()
        
        print("清理状态信息:")
        for key, value in cleanup_status.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"测试失败: {e}")


def interactive_test():
    """交互式测试"""
    print("\n" + "=" * 60)
    print("交互式测试")
    print("=" * 60)
    
    try:
        # 创建应用程序实例
        app = KafkaDataFilterApp("filter_config.yaml")
        
        print("应用程序已启动")
        print("可用命令:")
        print("  status  - 查看清理状态")
        print("  cleanup - 执行手动清理")
        print("  exit    - 正常退出")
        print("  ctrl+c  - 信号退出")
        
        while True:
            try:
                command = input("\n请输入命令: ").strip().lower()
                
                if command == "status":
                    cleanup_status = app.get_cleanup_status()
                    print("清理状态:")
                    for key, value in cleanup_status.items():
                        print(f"  {key}: {value}")
                
                elif command == "cleanup":
                    print("执行手动清理...")
                    success = app.manual_cleanup_consumer_group(force=True)
                    if success:
                        print("✅ 手动清理成功")
                    else:
                        print("❌ 手动清理失败")
                
                elif command == "exit":
                    print("正常退出...")
                    app.stop()
                    break
                
                else:
                    print("未知命令，请重新输入")
                    
            except KeyboardInterrupt:
                print("\n收到中断信号，程序将退出...")
                break
        
    except Exception as e:
        print(f"交互式测试失败: {e}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="自动清理功能测试工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
测试模式:
  normal    - 测试正常退出时的自动清理
  signal    - 测试信号退出时的自动清理
  manual    - 测试手动清理功能
  status    - 测试清理状态查询
  interactive - 交互式测试
  all       - 运行所有测试
        """
    )
    
    parser.add_argument(
        "mode",
        choices=["normal", "signal", "manual", "status", "interactive", "all"],
        help="测试模式"
    )
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🧪 自动清理功能测试")
    print(f"测试模式: {args.mode}")
    
    try:
        if args.mode == "normal":
            test_normal_exit()
        
        elif args.mode == "signal":
            test_signal_exit()
        
        elif args.mode == "manual":
            test_manual_cleanup()
        
        elif args.mode == "status":
            test_cleanup_status()
        
        elif args.mode == "interactive":
            interactive_test()
        
        elif args.mode == "all":
            test_status()
            test_manual_cleanup()
            test_normal_exit()
            # 注意: signal测试会终止程序，所以放在最后
            print("\n⚠️ 信号测试将终止程序，请手动运行:")
            print("python test_auto_cleanup.py signal")
        
        print("\n✅ 测试完成")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
