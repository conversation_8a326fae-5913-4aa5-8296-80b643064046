2025-08-21 18:11:47,257 - kafka_data_filter.main - INFO - 日志系统已配置，级别: INFO
2025-08-21 18:11:47,257 - kafka_data_filter.main - INFO - 开始初始化Kafka数据过滤程序
2025-08-21 18:11:47,257 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-21 18:11:47,257 - kafka_data_filter.main - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-21 18:11:47,257 - kafka_data_filter.output_handler - INFO - 文件输出已初始化: filtered_messages.json
2025-08-21 18:11:47,257 - kafka_data_filter.main - INFO - 输出处理器已初始化
2025-08-21 18:11:47,257 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-21 18:11:47,257 - kafka_data_filter.main - INFO - 统计模块已初始化
2025-08-21 18:11:47,257 - kafka_data_filter.main - INFO - Kafka消费者已初始化
2025-08-21 18:11:47,257 - kafka_data_filter.main - INFO - 所有组件初始化完成
2025-08-21 18:11:47,257 - kafka_data_filter.main - INFO - 启动Kafka数据过滤程序
2025-08-21 18:11:47,257 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-21 18:11:47,262 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 18:11:47,263 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-21 18:11:47,342 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 18:11:47,525 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-21 18:11:47,526 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-21 18:11:47,527 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-21 18:11:47,527 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-21 18:11:47,527 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x10648b1a0>}
2025-08-21 18:11:47,527 - kafka_data_filter.main - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-21 18:11:47,527 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-21 18:11:47,601 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-21 18:11:47,601 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-21 18:11:47,601 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-21 18:11:47,602 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-21 18:11:47,603 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 18:11:47,707 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 18:11:47,707 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 18:11:47,813 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-21 18:11:47,958 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-21 18:11:47,959 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 18:11:48,028 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 18:11:48,301 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 5
2025-08-21 18:11:48,302 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-21 18:11:48,302 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-21 18:12:34,623 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 100
2025-08-21 18:13:20,692 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 200
2025-08-21 18:13:51,263 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 300
2025-08-21 18:14:31,273 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 400
2025-08-21 18:15:02,236 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 500
2025-08-21 18:15:34,938 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 600
2025-08-21 18:16:06,597 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 700
2025-08-21 18:16:39,467 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 800
2025-08-21 18:17:28,930 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 900
2025-08-21 18:18:11,131 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1000
2025-08-21 18:19:29,045 - kafka_data_filter.main - INFO - 收到信号 SIGINT，准备停止程序
2025-08-21 18:19:29,046 - kafka_data_filter.main - INFO - 正在停止应用程序...
2025-08-21 18:19:29,046 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-21 18:19:29,502 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 1029, 错误数: 0
2025-08-21 18:19:29,502 - kafka_data_filter.main - INFO - 开始清理资源
2025-08-21 18:19:29,502 - kafka_data_filter.main - INFO - 最终统计信息:
2025-08-21 18:19:29,502 - kafka_data_filter.main - INFO - 运行时间: 462.25 秒
消费消息数: 1029
处理消息数: 1029
通过过滤数: 2
被过滤数: 1027
输出消息数: 2
错误数: 0
通过率: 0.19%
平均处理时间: 0.02 ms
当前吞吐量: 6318.35 消息/秒
总体吞吐量: 2.23 消息/秒
2025-08-21 18:19:30,613 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-21 18:19:30,614 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-21 18:19:31,087 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 18:19:31,088 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 18:19:31,089 - kafka.consumer.fetcher - ERROR - Fetch to node 0 failed: Cancelled: <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>
2025-08-21 18:19:31,089 - kafka_data_filter.kafka_consumer - INFO - Kafka消费者连接已关闭
2025-08-21 18:19:31,089 - kafka_data_filter.main - INFO - Kafka消费者已关闭
2025-08-21 18:19:31,090 - kafka_data_filter.output_handler - INFO - 文件输出已关闭
2025-08-21 18:19:31,090 - kafka_data_filter.main - INFO - 输出处理器已关闭
2025-08-21 18:19:31,090 - kafka_data_filter.main - INFO - 资源清理完成
2025-08-21 18:33:17,136 - kafka_data_filter.main - INFO - 日志系统已配置，级别: INFO
2025-08-21 18:33:17,137 - kafka_data_filter.main - INFO - 开始初始化Kafka数据过滤程序
2025-08-21 18:33:17,137 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-21 18:33:17,137 - kafka_data_filter.main - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-21 18:33:17,137 - kafka_data_filter.output_handler - INFO - 文件输出已初始化: filtered_messages2.json
2025-08-21 18:33:17,137 - kafka_data_filter.main - INFO - 输出处理器已初始化
2025-08-21 18:33:17,137 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-21 18:33:17,137 - kafka_data_filter.main - INFO - 统计模块已初始化
2025-08-21 18:33:17,137 - kafka_data_filter.main - INFO - Kafka消费者已初始化
2025-08-21 18:33:17,137 - kafka_data_filter.main - INFO - 所有组件初始化完成
2025-08-21 18:33:17,137 - kafka_data_filter.main - INFO - 启动Kafka数据过滤程序
2025-08-21 18:33:17,137 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-21 18:33:17,141 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 18:33:17,141 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-21 18:33:18,224 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 18:33:18,404 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-21 18:33:18,404 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-21 18:33:18,405 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-21 18:33:18,406 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-21 18:33:18,406 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x1067b71a0>}
2025-08-21 18:33:18,406 - kafka_data_filter.main - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-21 18:33:18,406 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-21 18:33:18,484 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-21 18:33:18,484 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-21 18:33:18,485 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-21 18:33:18,485 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-21 18:33:18,486 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 18:33:18,590 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 18:33:18,591 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 18:33:18,697 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-21 18:33:18,779 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-21 18:33:18,780 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-21 18:33:18,870 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-21 18:33:18,895 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 7
2025-08-21 18:33:18,895 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-21 18:33:18,896 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-21 18:33:53,784 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 100
2025-08-21 18:34:23,768 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 200
2025-08-21 18:35:02,863 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 300
2025-08-21 18:35:26,228 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 400
2025-08-21 18:35:52,397 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 500
2025-08-21 18:36:17,827 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 600
2025-08-21 18:36:57,336 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 700
2025-08-21 18:37:37,889 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 800
2025-08-21 18:37:58,490 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 900
2025-08-21 18:38:23,225 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1000
2025-08-21 18:38:58,705 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1100
2025-08-21 18:39:22,581 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1200
2025-08-21 18:39:50,198 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1300
2025-08-21 18:40:23,849 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1400
2025-08-21 18:40:50,142 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1500
2025-08-21 18:41:14,381 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 1600
2025-08-21 18:41:35,961 - kafka_data_filter.main - INFO - 收到信号 SIGINT，准备停止程序
2025-08-21 18:41:35,963 - kafka_data_filter.main - INFO - 正在停止应用程序...
2025-08-21 18:41:35,963 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-21 18:41:36,487 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 1653, 错误数: 0
2025-08-21 18:41:36,488 - kafka_data_filter.main - INFO - 开始清理资源
2025-08-21 18:41:36,489 - kafka_data_filter.main - INFO - 最终统计信息:
2025-08-21 18:41:36,489 - kafka_data_filter.main - INFO - 运行时间: 499.35 秒
消费消息数: 1653
处理消息数: 1653
通过过滤数: 7
被过滤数: 1646
输出消息数: 7
错误数: 0
通过率: 0.42%
平均处理时间: 0.02 ms
当前吞吐量: 4.1 消息/秒
总体吞吐量: 3.31 消息/秒
2025-08-21 18:41:36,555 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-21 18:41:36,555 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-21 18:41:36,634 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 18:41:36,636 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-21 18:41:36,636 - kafka.consumer.fetcher - ERROR - Fetch to node 0 failed: Cancelled: <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>
2025-08-21 18:41:36,636 - kafka_data_filter.kafka_consumer - INFO - Kafka消费者连接已关闭
2025-08-21 18:41:36,636 - kafka_data_filter.main - INFO - Kafka消费者已关闭
2025-08-21 18:41:36,637 - kafka_data_filter.output_handler - INFO - 文件输出已关闭
2025-08-21 18:41:36,637 - kafka_data_filter.main - INFO - 输出处理器已关闭
2025-08-21 18:41:36,637 - kafka_data_filter.main - INFO - 资源清理完成
2025-08-22 09:54:40,405 - kafka_data_filter.main - INFO - 日志系统已配置，级别: INFO
2025-08-22 09:54:40,405 - kafka_data_filter.main - INFO - 开始初始化Kafka数据过滤程序
2025-08-22 09:54:40,406 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-22 09:54:40,406 - kafka_data_filter.main - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-22 09:54:40,407 - kafka_data_filter.output_handler - INFO - 文件输出已初始化: filtered_messages2.json
2025-08-22 09:54:40,407 - kafka_data_filter.main - INFO - 输出处理器已初始化
2025-08-22 09:54:40,407 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-22 09:54:40,407 - kafka_data_filter.main - INFO - 统计模块已初始化
2025-08-22 09:54:40,407 - kafka_data_filter.main - INFO - Kafka消费者已初始化
2025-08-22 09:54:40,407 - kafka_data_filter.kafka_consumer_group_manager - ERROR - 连接Kafka集群失败: KafkaConfigurationError: Unrecognized configs: {'enable_auto_commit', 'auto_offset_reset'}
2025-08-22 09:54:40,407 - kafka_data_filter.auto_cleanup_manager - ERROR - 初始化消费者组管理器失败: 无法连接到Kafka集群: KafkaConfigurationError: Unrecognized configs: {'enable_auto_commit', 'auto_offset_reset'}
2025-08-22 09:54:40,407 - kafka_data_filter.auto_cleanup_manager - INFO - 清理处理器注册成功
2025-08-22 09:54:40,407 - kafka_data_filter.auto_cleanup_manager - INFO - 自动清理管理器已初始化，目标消费者组: moye-check-data
2025-08-22 09:54:40,407 - kafka_data_filter.main - INFO - 自动清理管理器已初始化
2025-08-22 09:54:40,407 - kafka_data_filter.main - INFO - 所有组件初始化完成
2025-08-22 09:54:40,407 - kafka_data_filter.main - INFO - 启动Kafka数据过滤程序
2025-08-22 09:54:40,407 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-22 09:54:40,414 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 09:54:40,414 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 09:54:40,476 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 09:54:40,653 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 09:54:40,654 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 09:54:40,655 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-22 09:54:40,655 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-22 09:54:40,655 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x103e3cd60>}
2025-08-22 09:54:40,655 - kafka_data_filter.main - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-22 09:54:40,655 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-22 09:54:40,717 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-22 09:54:40,717 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-22 09:54:40,718 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-22 09:54:40,718 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-22 09:54:40,718 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 09:54:40,824 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 09:54:40,824 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 09:54:40,928 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-22 09:54:40,997 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-22 09:54:40,998 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 09:54:41,056 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 09:54:41,089 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 9
2025-08-22 09:54:41,090 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-22 09:54:41,091 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-22 09:54:41,429 - kafka.consumer.fetcher - INFO - Fetch offset 9966668 is out of range for topic-partition TopicPartition(topic='moyeT_dfycb_website', partition=0)
2025-08-22 09:55:34,894 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 100
2025-08-22 09:57:32,819 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 200
2025-08-22 09:58:01,197 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 300
2025-08-22 09:58:30,481 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 400
2025-08-22 09:58:59,178 - kafka_data_filter.main - INFO - 收到信号 SIGINT，准备停止程序
2025-08-22 09:58:59,179 - kafka_data_filter.main - INFO - 正在停止应用程序...
2025-08-22 09:58:59,179 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 09:58:59,495 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 491, 错误数: 0
2025-08-22 09:58:59,495 - kafka_data_filter.main - INFO - 开始清理资源
2025-08-22 09:58:59,495 - kafka_data_filter.main - INFO - 最终统计信息:
2025-08-22 09:58:59,495 - kafka_data_filter.main - INFO - 运行时间: 259.09 秒
消费消息数: 491
处理消息数: 491
通过过滤数: 3
被过滤数: 488
输出消息数: 3
错误数: 0
通过率: 0.61%
平均处理时间: 0.07 ms
当前吞吐量: 3.33 消息/秒
总体吞吐量: 1.9 消息/秒
2025-08-22 09:58:59,496 - kafka_data_filter.kafka_consumer - INFO - 开始优雅关闭Kafka消费者...
2025-08-22 09:58:59,496 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 09:58:59,496 - kafka_data_filter.kafka_consumer - INFO - 提交当前offset...
2025-08-22 09:58:59,556 - kafka_data_filter.kafka_consumer - INFO - Offset提交成功
2025-08-22 09:58:59,557 - kafka_data_filter.kafka_consumer - INFO - 取消topic订阅...
2025-08-22 09:58:59,557 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-22 09:58:59,557 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-22 09:58:59,618 - kafka_data_filter.kafka_consumer - INFO - 取消订阅成功
2025-08-22 09:58:59,618 - kafka_data_filter.kafka_consumer - INFO - 关闭消费者连接...
2025-08-22 09:58:59,618 - kafka_data_filter.kafka_consumer - ERROR - 关闭消费者失败: KafkaConsumer.close() got an unexpected keyword argument 'timeout'
2025-08-22 09:58:59,618 - kafka_data_filter.main - WARNING - 优雅关闭失败，执行强制关闭
2025-08-22 09:58:59,618 - kafka_data_filter.main - INFO - Kafka消费者已关闭
2025-08-22 09:58:59,619 - kafka_data_filter.output_handler - INFO - 文件输出已关闭
2025-08-22 09:58:59,619 - kafka_data_filter.main - INFO - 输出处理器已关闭
2025-08-22 09:58:59,619 - kafka_data_filter.main - INFO - 自动清理管理器将在程序退出时执行清理
2025-08-22 09:58:59,619 - kafka_data_filter.main - INFO - 资源清理完成
2025-08-22 09:58:59,619 - kafka_data_filter.auto_cleanup_manager - INFO - 程序正常退出，执行自动清理...
2025-08-22 09:58:59,619 - kafka_data_filter.auto_cleanup_manager - INFO - 开始执行消费者组清理操作，原因: 正常退出
2025-08-22 09:58:59,619 - kafka_data_filter.auto_cleanup_manager - WARNING - 消费者组管理器未初始化，跳过清理
2025-08-22 15:50:25,023 - kafka_data_filter.main - INFO - 日志系统已配置，级别: INFO
2025-08-22 15:50:25,024 - kafka_data_filter.main - INFO - 开始初始化Kafka数据过滤程序
2025-08-22 15:50:25,024 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-22 15:50:25,024 - kafka_data_filter.main - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-22 15:50:25,024 - kafka_data_filter.output_handler - INFO - 文件输出已初始化: filtered_messages3.json
2025-08-22 15:50:25,024 - kafka_data_filter.main - INFO - 输出处理器已初始化
2025-08-22 15:50:25,024 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-22 15:50:25,024 - kafka_data_filter.main - INFO - 统计模块已初始化
2025-08-22 15:50:25,024 - kafka_data_filter.main - INFO - Kafka消费者已初始化
2025-08-22 15:50:25,030 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 15:50:25,030 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 15:50:25,101 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 15:50:25,270 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 15:50:25,270 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 15:50:25,270 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 15:50:25,441 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 15:50:25,441 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 15:50:25,606 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 15:50:25,606 - kafka.conn - INFO - Probing node 0 broker version
2025-08-22 15:50:25,666 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 15:50:25,666 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 15:50:25,909 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 15:50:25,909 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 15:50:25,909 - kafka_data_filter.kafka_consumer_group_manager - INFO - 成功连接到Kafka集群: **************:19301
2025-08-22 15:50:25,909 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组管理器初始化成功
2025-08-22 15:50:25,909 - kafka_data_filter.auto_cleanup_manager - INFO - 清理处理器注册成功
2025-08-22 15:50:25,909 - kafka_data_filter.auto_cleanup_manager - INFO - 自动清理管理器已初始化，目标消费者组: moye-check-data
2025-08-22 15:50:25,909 - kafka_data_filter.main - INFO - 自动清理管理器已初始化
2025-08-22 15:50:25,909 - kafka_data_filter.main - INFO - 所有组件初始化完成
2025-08-22 15:50:25,909 - kafka_data_filter.main - INFO - 启动Kafka数据过滤程序
2025-08-22 15:50:25,909 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-22 15:50:25,909 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 15:50:25,910 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 15:50:25,974 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 15:50:26,143 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 15:50:26,143 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 15:50:26,143 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-22 15:50:26,143 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-22 15:50:26,143 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x104fc8d60>}
2025-08-22 15:50:26,143 - kafka_data_filter.main - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-22 15:50:26,143 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-22 15:50:26,213 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-22 15:50:26,213 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-22 15:50:26,213 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-22 15:50:26,213 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-22 15:50:26,213 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 15:50:26,318 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 15:50:26,319 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 15:50:26,419 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-22 15:50:26,508 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-22 15:50:26,508 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 15:50:26,585 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 15:50:26,585 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 11
2025-08-22 15:50:26,585 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-22 15:50:26,585 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-22 15:50:52,537 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 100
2025-08-22 15:51:17,680 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 200
2025-08-22 15:51:38,039 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 300
2025-08-22 15:51:48,537 - kafka_data_filter.main - INFO - 收到信号 SIGINT，准备停止程序
2025-08-22 15:51:48,538 - kafka_data_filter.main - INFO - 正在停止应用程序...
2025-08-22 15:51:48,538 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 15:51:48,805 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 393, 错误数: 0
2025-08-22 15:51:48,806 - kafka_data_filter.main - INFO - 开始清理资源
2025-08-22 15:51:48,806 - kafka_data_filter.main - INFO - 最终统计信息:
2025-08-22 15:51:48,807 - kafka_data_filter.main - INFO - 运行时间: 82.9 秒
消费消息数: 393
处理消息数: 393
通过过滤数: 2
被过滤数: 391
输出消息数: 2
错误数: 0
通过率: 0.51%
平均处理时间: 0.02 ms
当前吞吐量: 5.57 消息/秒
总体吞吐量: 4.74 消息/秒
2025-08-22 15:51:48,807 - kafka_data_filter.kafka_consumer - INFO - 开始优雅关闭Kafka消费者...
2025-08-22 15:51:48,807 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 15:51:48,808 - kafka_data_filter.kafka_consumer - INFO - 提交当前offset...
2025-08-22 15:51:48,877 - kafka_data_filter.kafka_consumer - INFO - Offset提交成功
2025-08-22 15:51:48,878 - kafka_data_filter.kafka_consumer - INFO - 取消topic订阅...
2025-08-22 15:51:48,878 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-22 15:51:48,878 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-22 15:51:48,951 - kafka_data_filter.kafka_consumer - INFO - 取消订阅成功
2025-08-22 15:51:48,952 - kafka_data_filter.kafka_consumer - INFO - 关闭消费者连接...
2025-08-22 15:51:48,952 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 15:51:48,952 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 15:51:48,953 - kafka.consumer.fetcher - ERROR - Fetch to node 0 failed: Cancelled: <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>
2025-08-22 15:51:48,953 - kafka_data_filter.kafka_consumer - INFO - 消费者连接已关闭
2025-08-22 15:51:48,953 - kafka_data_filter.main - INFO - Kafka消费者已关闭
2025-08-22 15:51:48,954 - kafka_data_filter.output_handler - INFO - 文件输出已关闭
2025-08-22 15:51:48,954 - kafka_data_filter.main - INFO - 输出处理器已关闭
2025-08-22 15:51:48,954 - kafka_data_filter.main - INFO - 自动清理管理器将在程序退出时执行清理
2025-08-22 15:51:48,954 - kafka_data_filter.main - INFO - 资源清理完成
2025-08-22 15:51:48,955 - kafka_data_filter.auto_cleanup_manager - INFO - 程序正常退出，执行自动清理...
2025-08-22 15:51:48,955 - kafka_data_filter.auto_cleanup_manager - INFO - 开始执行消费者组清理操作，原因: 正常退出
2025-08-22 15:51:49,166 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组 moye-check-data 不存在，无需清理
2025-08-22 15:51:49,166 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 15:51:49,167 - kafka_data_filter.kafka_consumer_group_manager - INFO - Kafka管理客户端已关闭
2025-08-22 15:53:06,216 - kafka_data_filter.main - INFO - 日志系统已配置，级别: INFO
2025-08-22 15:53:06,216 - kafka_data_filter.main - INFO - 开始初始化Kafka数据过滤程序
2025-08-22 15:53:06,216 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-22 15:53:06,216 - kafka_data_filter.main - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-22 15:53:06,216 - kafka_data_filter.output_handler - INFO - 文件输出已初始化: filtered_messages3.json
2025-08-22 15:53:06,216 - kafka_data_filter.main - INFO - 输出处理器已初始化
2025-08-22 15:53:06,216 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-22 15:53:06,216 - kafka_data_filter.main - INFO - 统计模块已初始化
2025-08-22 15:53:06,216 - kafka_data_filter.main - INFO - Kafka消费者已初始化
2025-08-22 15:53:06,221 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 15:53:06,221 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 15:53:06,287 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 15:53:06,454 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 15:53:06,455 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 15:53:06,455 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 15:53:06,626 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 15:53:06,627 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 15:53:06,754 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 15:53:06,754 - kafka.conn - INFO - Probing node 0 broker version
2025-08-22 15:53:06,815 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 15:53:06,816 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 15:53:06,980 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 15:53:06,981 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 15:53:06,981 - kafka_data_filter.kafka_consumer_group_manager - INFO - 成功连接到Kafka集群: **************:19301
2025-08-22 15:53:06,981 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组管理器初始化成功
2025-08-22 15:53:06,981 - kafka_data_filter.auto_cleanup_manager - INFO - 清理处理器注册成功
2025-08-22 15:53:06,981 - kafka_data_filter.auto_cleanup_manager - INFO - 自动清理管理器已初始化，目标消费者组: moye-check-data
2025-08-22 15:53:06,981 - kafka_data_filter.main - INFO - 自动清理管理器已初始化
2025-08-22 15:53:06,981 - kafka_data_filter.main - INFO - 所有组件初始化完成
2025-08-22 15:53:06,981 - kafka_data_filter.main - INFO - 启动Kafka数据过滤程序
2025-08-22 15:53:06,982 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-22 15:53:06,983 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 15:53:06,983 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 15:53:07,054 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 15:53:07,224 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 15:53:07,225 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 15:53:07,226 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-22 15:53:07,226 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-22 15:53:07,226 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x105f44d60>}
2025-08-22 15:53:07,226 - kafka_data_filter.main - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-22 15:53:07,227 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-22 15:53:07,296 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-22 15:53:07,296 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-22 15:53:07,297 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-22 15:53:07,297 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-22 15:53:07,298 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 15:53:07,404 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 15:53:07,405 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 15:53:07,510 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-22 15:53:07,600 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-22 15:53:07,602 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 15:53:07,666 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 15:53:07,667 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 13
2025-08-22 15:53:07,667 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-22 15:53:07,667 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-22 15:53:28,954 - kafka_data_filter.main - INFO - 收到信号 SIGINT，准备停止程序
2025-08-22 15:53:28,955 - kafka_data_filter.main - INFO - 正在停止应用程序...
2025-08-22 15:53:28,955 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 15:53:29,119 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 86, 错误数: 0
2025-08-22 15:53:29,119 - kafka_data_filter.main - INFO - 开始清理资源
2025-08-22 15:53:29,120 - kafka_data_filter.main - INFO - 最终统计信息:
2025-08-22 15:53:29,120 - kafka_data_filter.main - INFO - 运行时间: 22.14 秒
消费消息数: 86
处理消息数: 86
通过过滤数: 0
被过滤数: 86
输出消息数: 0
错误数: 0
通过率: 0.0%
平均处理时间: 0.01 ms
当前吞吐量: 10.61 消息/秒
总体吞吐量: 3.88 消息/秒
2025-08-22 15:53:29,120 - kafka_data_filter.kafka_consumer - INFO - 开始优雅关闭Kafka消费者...
2025-08-22 15:53:29,120 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 15:53:29,120 - kafka_data_filter.kafka_consumer - INFO - 提交当前offset...
2025-08-22 15:53:29,178 - kafka_data_filter.kafka_consumer - INFO - Offset提交成功
2025-08-22 15:53:29,178 - kafka_data_filter.kafka_consumer - INFO - 取消topic订阅...
2025-08-22 15:53:29,178 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-22 15:53:29,178 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-22 15:53:29,235 - kafka_data_filter.kafka_consumer - INFO - 取消订阅成功
2025-08-22 15:53:29,235 - kafka_data_filter.kafka_consumer - INFO - 关闭消费者连接...
2025-08-22 15:53:29,235 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 15:53:29,235 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 15:53:29,235 - kafka_data_filter.kafka_consumer - INFO - 消费者连接已关闭
2025-08-22 15:53:29,235 - kafka_data_filter.main - INFO - Kafka消费者已关闭
2025-08-22 15:53:29,235 - kafka_data_filter.output_handler - INFO - 文件输出已关闭
2025-08-22 15:53:29,235 - kafka_data_filter.main - INFO - 输出处理器已关闭
2025-08-22 15:53:29,235 - kafka_data_filter.main - INFO - 自动清理管理器将在程序退出时执行清理
2025-08-22 15:53:29,235 - kafka_data_filter.main - INFO - 资源清理完成
2025-08-22 15:53:29,235 - kafka_data_filter.auto_cleanup_manager - INFO - 程序正常退出，执行自动清理...
2025-08-22 15:53:29,235 - kafka_data_filter.auto_cleanup_manager - INFO - 开始执行消费者组清理操作，原因: 正常退出
2025-08-22 15:53:29,384 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组 moye-check-data 不存在，无需清理
2025-08-22 15:53:29,384 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 15:53:29,385 - kafka_data_filter.kafka_consumer_group_manager - INFO - Kafka管理客户端已关闭
2025-08-22 16:04:20,410 - kafka_data_filter.main - INFO - 日志系统已配置，级别: INFO
2025-08-22 16:04:20,411 - kafka_data_filter.main - INFO - 开始初始化Kafka数据过滤程序
2025-08-22 16:04:20,411 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-22 16:04:20,411 - kafka_data_filter.main - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-22 16:04:20,411 - kafka_data_filter.output_handler - INFO - 文件输出已初始化: filtered_messages3.json
2025-08-22 16:04:20,411 - kafka_data_filter.main - INFO - 输出处理器已初始化
2025-08-22 16:04:20,411 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-22 16:04:20,411 - kafka_data_filter.main - INFO - 统计模块已初始化
2025-08-22 16:04:20,411 - kafka_data_filter.main - INFO - Kafka消费者已初始化
2025-08-22 16:04:20,416 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 16:04:20,416 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 16:04:20,483 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 16:04:20,661 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 16:04:20,661 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 16:04:20,661 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 16:04:20,847 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 16:04:20,848 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 16:04:21,001 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 16:04:21,001 - kafka.conn - INFO - Probing node 0 broker version
2025-08-22 16:04:21,065 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 16:04:21,065 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 16:04:21,259 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 16:04:21,259 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 16:04:21,259 - kafka_data_filter.kafka_consumer_group_manager - INFO - 成功连接到Kafka集群: **************:19301
2025-08-22 16:04:21,259 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组管理器初始化成功
2025-08-22 16:04:21,259 - kafka_data_filter.auto_cleanup_manager - INFO - 清理处理器注册成功
2025-08-22 16:04:21,259 - kafka_data_filter.auto_cleanup_manager - INFO - 自动清理管理器已初始化，目标消费者组: moye-check-data
2025-08-22 16:04:21,259 - kafka_data_filter.main - INFO - 自动清理管理器已初始化
2025-08-22 16:04:21,259 - kafka_data_filter.main - INFO - 所有组件初始化完成
2025-08-22 16:04:21,259 - kafka_data_filter.main - INFO - 启动Kafka数据过滤程序
2025-08-22 16:04:21,259 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-22 16:04:21,260 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 16:04:21,260 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 16:04:21,324 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 16:04:21,492 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 16:04:21,493 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 16:04:21,493 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-22 16:04:21,493 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-22 16:04:21,493 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x105d58f40>}
2025-08-22 16:04:21,493 - kafka_data_filter.main - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-22 16:04:21,493 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-22 16:04:21,556 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-22 16:04:21,556 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-22 16:04:21,556 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-22 16:04:21,556 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-22 16:04:21,556 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 16:04:21,661 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 16:04:21,662 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 16:04:21,767 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-22 16:04:21,836 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-22 16:04:21,837 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 16:04:21,900 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 16:04:21,901 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 15
2025-08-22 16:04:21,901 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-22 16:04:21,901 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-22 16:04:53,919 - kafka_data_filter.kafka_consumer - INFO - 已处理消息数: 100
2025-08-22 16:04:54,161 - kafka_data_filter.main - INFO - 收到信号 SIGINT，准备停止程序
2025-08-22 16:04:54,161 - kafka_data_filter.main - INFO - 正在停止应用程序...
2025-08-22 16:04:54,162 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 16:04:54,925 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 132, 错误数: 0
2025-08-22 16:04:54,925 - kafka_data_filter.main - INFO - 开始清理资源
2025-08-22 16:04:54,926 - kafka_data_filter.main - INFO - 最终统计信息:
2025-08-22 16:04:54,926 - kafka_data_filter.main - INFO - 运行时间: 33.67 秒
消费消息数: 132
处理消息数: 132
通过过滤数: 0
被过滤数: 132
输出消息数: 0
错误数: 0
通过率: 0.0%
平均处理时间: 0.02 ms
当前吞吐量: 6.02 消息/秒
总体吞吐量: 3.92 消息/秒
2025-08-22 16:04:54,927 - kafka_data_filter.kafka_consumer - INFO - 开始优雅关闭Kafka消费者...
2025-08-22 16:04:54,927 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 16:04:54,927 - kafka_data_filter.kafka_consumer - INFO - 提交当前offset...
2025-08-22 16:04:54,996 - kafka_data_filter.kafka_consumer - INFO - Offset提交成功
2025-08-22 16:04:54,996 - kafka_data_filter.kafka_consumer - INFO - 取消topic订阅...
2025-08-22 16:04:54,996 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-22 16:04:54,996 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-22 16:04:55,074 - kafka_data_filter.kafka_consumer - INFO - 取消订阅成功
2025-08-22 16:04:55,075 - kafka_data_filter.kafka_consumer - INFO - 关闭消费者连接...
2025-08-22 16:04:55,075 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 16:04:55,075 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 16:04:55,075 - kafka.consumer.fetcher - ERROR - Fetch to node 0 failed: Cancelled: <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>
2025-08-22 16:04:55,075 - kafka_data_filter.kafka_consumer - INFO - 消费者连接已关闭
2025-08-22 16:04:55,075 - kafka_data_filter.main - INFO - Kafka消费者已关闭
2025-08-22 16:04:55,075 - kafka_data_filter.output_handler - INFO - 文件输出已关闭
2025-08-22 16:04:55,075 - kafka_data_filter.main - INFO - 输出处理器已关闭
2025-08-22 16:04:55,075 - kafka_data_filter.main - INFO - 自动清理管理器将在程序退出时执行清理
2025-08-22 16:04:55,075 - kafka_data_filter.main - INFO - 资源清理完成
2025-08-22 16:04:55,075 - kafka_data_filter.auto_cleanup_manager - INFO - 程序正常退出，执行自动清理...
2025-08-22 16:04:55,075 - kafka_data_filter.auto_cleanup_manager - INFO - 开始执行消费者组清理操作，原因: 正常退出
2025-08-22 16:04:55,075 - kafka_data_filter.auto_cleanup_manager - INFO - 等待Kafka集群状态稳定...
2025-08-22 16:04:57,080 - kafka_data_filter.auto_cleanup_manager - INFO - 检查消费者组 moye-check-data 是否存在...
2025-08-22 16:04:57,139 - kafka_data_filter.kafka_consumer_group_manager - ERROR - 检查消费者组存在性失败: 'tuple' object has no attribute 'group_id'
2025-08-22 16:04:57,139 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组存在性检查结果: exists=False, info=error_'tuple' object has no attribute 'group_id'
2025-08-22 16:04:57,139 - kafka_data_filter.auto_cleanup_manager - WARNING - 检查消费者组存在性时出错: error_'tuple' object has no attribute 'group_id'，跳过清理
2025-08-22 16:04:57,139 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 16:04:57,140 - kafka_data_filter.kafka_consumer_group_manager - INFO - Kafka管理客户端已关闭
2025-08-22 16:13:50,261 - kafka_data_filter.main - INFO - 日志系统已配置，级别: INFO
2025-08-22 16:13:50,261 - kafka_data_filter.main - INFO - 开始初始化Kafka数据过滤程序
2025-08-22 16:13:50,261 - kafka_data_filter.data_filter - INFO - 成功解析 2 个过滤规则
2025-08-22 16:13:50,261 - kafka_data_filter.main - INFO - 数据过滤器已初始化，规则数量: 2
2025-08-22 16:13:50,262 - kafka_data_filter.output_handler - INFO - 文件输出已初始化: filtered_messages3.json
2025-08-22 16:13:50,262 - kafka_data_filter.main - INFO - 输出处理器已初始化
2025-08-22 16:13:50,262 - kafka_data_filter.statistics - INFO - 统计模块已初始化，启用状态: True
2025-08-22 16:13:50,262 - kafka_data_filter.main - INFO - 统计模块已初始化
2025-08-22 16:13:50,262 - kafka_data_filter.main - INFO - Kafka消费者已初始化
2025-08-22 16:13:50,267 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 16:13:50,267 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 16:13:50,352 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 16:13:50,643 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 16:13:50,644 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 16:13:50,644 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 16:13:50,814 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 16:13:50,814 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 16:13:50,947 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 16:13:50,947 - kafka.conn - INFO - Probing node 0 broker version
2025-08-22 16:13:51,018 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 16:13:51,019 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 16:13:51,200 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 16:13:51,201 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 16:13:51,201 - kafka_data_filter.kafka_consumer_group_manager - INFO - 成功连接到Kafka集群: **************:19301
2025-08-22 16:13:51,201 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组管理器初始化成功
2025-08-22 16:13:51,201 - kafka_data_filter.auto_cleanup_manager - INFO - 清理处理器注册成功
2025-08-22 16:13:51,201 - kafka_data_filter.auto_cleanup_manager - INFO - 自动清理管理器已初始化，目标消费者组: moye-check-data
2025-08-22 16:13:51,201 - kafka_data_filter.main - INFO - 自动清理管理器已初始化
2025-08-22 16:13:51,201 - kafka_data_filter.main - INFO - 所有组件初始化完成
2025-08-22 16:13:51,201 - kafka_data_filter.main - INFO - 启动Kafka数据过滤程序
2025-08-22 16:13:51,202 - kafka_data_filter.statistics - INFO - 统计已开始
2025-08-22 16:13:51,203 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 16:13:51,204 - kafka.conn - INFO - Probing node bootstrap-0 broker version
2025-08-22 16:13:51,287 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 16:13:51,456 - kafka.conn - INFO - Broker version identified as 2.6.0
2025-08-22 16:13:51,456 - kafka.conn - INFO - Set configuration api_version=(2, 6, 0) to skip auto check_version requests on startup
2025-08-22 16:13:51,457 - kafka.consumer.subscription_state - INFO - Updating subscribed topics to: ('moyeT_dfycb_website',)
2025-08-22 16:13:51,457 - kafka_data_filter.kafka_consumer - INFO - 成功连接到Kafka集群，订阅topic: moyeT_dfycb_website
2025-08-22 16:13:51,458 - kafka_data_filter.kafka_consumer - INFO - 消费者配置: {'bootstrap_servers': '**************:19301', 'group_id': 'moye-check-data', 'auto_offset_reset': 'latest', 'enable_auto_commit': True, 'value_deserializer': <function ConfigManager.get_consumer_config.<locals>.<lambda> at 0x104911080>}
2025-08-22 16:13:51,458 - kafka_data_filter.main - INFO - 开始消费topic: moyeT_dfycb_website
2025-08-22 16:13:51,458 - kafka_data_filter.kafka_consumer - INFO - 开始消费消息，最大消息数: 无限制
2025-08-22 16:13:51,538 - kafka.cluster - INFO - Group coordinator for moye-check-data is BrokerMetadata(nodeId='coordinator-0', host='**************', port=19301, rack=None)
2025-08-22 16:13:51,538 - kafka.coordinator - INFO - Discovered coordinator coordinator-0 for group moye-check-data
2025-08-22 16:13:51,538 - kafka.coordinator - INFO - Starting new heartbeat thread
2025-08-22 16:13:51,539 - kafka.coordinator.consumer - INFO - Revoking previously assigned partitions set() for group moye-check-data
2025-08-22 16:13:51,540 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 16:13:53,614 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 16:13:53,615 - kafka.conn - INFO - <BrokerConnection node_id=bootstrap-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 16:13:53,718 - kafka.coordinator - INFO - (Re-)joining group moye-check-data
2025-08-22 16:13:53,791 - kafka.coordinator - INFO - Elected group leader -- performing partition assignments using range
2025-08-22 16:13:53,792 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: connecting to **************:19301 [('**************', 19301) IPv4]
2025-08-22 16:13:53,867 - kafka.coordinator - INFO - Successfully joined group moye-check-data with generation 17
2025-08-22 16:13:53,867 - kafka.consumer.subscription_state - INFO - Updated partition assignment: [TopicPartition(topic='moyeT_dfycb_website', partition=0)]
2025-08-22 16:13:53,868 - kafka.coordinator.consumer - INFO - Setting newly assigned partitions {TopicPartition(topic='moyeT_dfycb_website', partition=0)} for group moye-check-data
2025-08-22 16:13:53,868 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connecting> [IPv4 ('**************', 19301)]>: Connection complete.
2025-08-22 16:14:03,443 - kafka_data_filter.main - INFO - 收到信号 SIGINT，准备停止程序
2025-08-22 16:14:03,444 - kafka_data_filter.main - INFO - 正在停止应用程序...
2025-08-22 16:14:03,444 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 16:14:04,213 - kafka_data_filter.kafka_consumer - INFO - 消费结束，总处理消息数: 0, 错误数: 0
2025-08-22 16:14:04,213 - kafka_data_filter.main - INFO - 开始清理资源
2025-08-22 16:14:04,213 - kafka_data_filter.main - INFO - 最终统计信息:
2025-08-22 16:14:04,214 - kafka_data_filter.main - INFO - 运行时间: 13.01 秒
消费消息数: 0
处理消息数: 0
通过过滤数: 0
被过滤数: 0
输出消息数: 0
错误数: 0
通过率: 0%
平均处理时间: 0 ms
当前吞吐量: 0.0 消息/秒
总体吞吐量: 0.0 消息/秒
2025-08-22 16:14:04,214 - kafka_data_filter.kafka_consumer - INFO - 开始优雅关闭Kafka消费者...
2025-08-22 16:14:04,214 - kafka_data_filter.kafka_consumer - INFO - 停止消费消息
2025-08-22 16:14:04,214 - kafka_data_filter.kafka_consumer - INFO - 提交当前offset...
2025-08-22 16:14:04,292 - kafka_data_filter.kafka_consumer - INFO - Offset提交成功
2025-08-22 16:14:04,292 - kafka_data_filter.kafka_consumer - INFO - 取消topic订阅...
2025-08-22 16:14:04,292 - kafka.coordinator - INFO - Stopping heartbeat thread
2025-08-22 16:14:04,293 - kafka.coordinator - INFO - Leaving consumer group (moye-check-data).
2025-08-22 16:14:04,366 - kafka_data_filter.kafka_consumer - INFO - 取消订阅成功
2025-08-22 16:14:04,367 - kafka_data_filter.kafka_consumer - INFO - 关闭消费者连接...
2025-08-22 16:14:04,367 - kafka.conn - INFO - <BrokerConnection node_id=coordinator-0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 16:14:04,367 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 16:14:04,367 - kafka.consumer.fetcher - ERROR - Fetch to node 0 failed: Cancelled: <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>
2025-08-22 16:14:04,367 - kafka_data_filter.kafka_consumer - INFO - 消费者连接已关闭
2025-08-22 16:14:04,367 - kafka_data_filter.main - INFO - Kafka消费者已关闭
2025-08-22 16:14:04,368 - kafka_data_filter.output_handler - INFO - 文件输出已关闭
2025-08-22 16:14:04,368 - kafka_data_filter.main - INFO - 输出处理器已关闭
2025-08-22 16:14:04,368 - kafka_data_filter.main - INFO - 自动清理管理器将在程序退出时执行清理
2025-08-22 16:14:04,368 - kafka_data_filter.main - INFO - 资源清理完成
2025-08-22 16:14:04,368 - kafka_data_filter.auto_cleanup_manager - INFO - 程序正常退出，执行自动清理...
2025-08-22 16:14:04,368 - kafka_data_filter.auto_cleanup_manager - INFO - 开始执行消费者组清理操作，原因: 正常退出
2025-08-22 16:14:04,368 - kafka_data_filter.auto_cleanup_manager - INFO - 等待Kafka集群状态稳定...
2025-08-22 16:14:06,373 - kafka_data_filter.auto_cleanup_manager - INFO - 检查消费者组 moye-check-data 是否存在...
2025-08-22 16:14:06,457 - kafka_data_filter.auto_cleanup_manager - INFO - 消费者组存在性检查结果: exists=True, info=found_in_list
2025-08-22 16:14:06,458 - kafka_data_filter.auto_cleanup_manager - INFO - 获取消费者组详细信息进行二次确认...
2025-08-22 16:14:07,788 - kafka_data_filter.kafka_consumer_group_manager - ERROR - 获取消费者组信息失败，已重试 2 次: 'list' object has no attribute 'keys'
2025-08-22 16:14:07,789 - kafka_data_filter.auto_cleanup_manager - INFO - 二次确认：消费者组 moye-check-data 不存在 (原因: exception, 错误: 'list' object has no attribute 'keys')
2025-08-22 16:14:07,789 - kafka.conn - INFO - <BrokerConnection node_id=0 host=**************:19301 <connected> [IPv4 ('**************', 19301)]>: Closing connection. 
2025-08-22 16:14:07,789 - kafka_data_filter.kafka_consumer_group_manager - INFO - Kafka管理客户端已关闭
