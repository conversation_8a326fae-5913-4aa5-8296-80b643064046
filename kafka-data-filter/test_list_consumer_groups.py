#!/usr/bin/env python3
"""
测试list_consumer_groups返回格式的脚本

专门用于调试和验证不同版本kafka-python库的返回格式
"""

import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.config_manager import ConfigManager
from kafka_data_filter.kafka_consumer_group_manager import KafkaConsumerGroupManager


def setup_logging():
    """配置日志系统"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler(sys.stdout)]
    )
    return logging.getLogger(__name__)


def test_list_consumer_groups_format():
    """测试list_consumer_groups的返回格式"""
    logger = setup_logging()
    
    print("🔍 测试 list_consumer_groups 返回格式")
    print("=" * 60)
    
    try:
        # 加载配置
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        
        print(f"Kafka服务器: {kafka_config['bootstrap_servers']}")
        
        # 创建消费者组管理器
        group_manager = KafkaConsumerGroupManager(
            bootstrap_servers=kafka_config['bootstrap_servers']
        )
        
        print("\n1. 直接调用 list_consumer_groups():")
        print("-" * 40)
        
        all_groups = group_manager.admin_client.list_consumer_groups()
        
        print(f"返回类型: {type(all_groups)}")
        print(f"返回长度: {len(all_groups) if all_groups else 0}")
        
        if all_groups:
            print(f"第一个元素类型: {type(all_groups[0])}")
            print(f"第一个元素内容: {all_groups[0]}")
            print(f"第一个元素的属性: {dir(all_groups[0])}")
            
            # 如果有多个元素，也打印几个
            for i, group in enumerate(all_groups[:3]):  # 只打印前3个
                print(f"\n元素 {i}:")
                print(f"  类型: {type(group)}")
                print(f"  内容: {group}")
                
                if hasattr(group, '__dict__'):
                    print(f"  __dict__: {group.__dict__}")
        
        print("\n2. 测试 _extract_group_ids() 方法:")
        print("-" * 40)
        
        group_ids = group_manager._extract_group_ids(all_groups)
        print(f"提取的消费者组ID: {group_ids}")
        
        print("\n3. 测试 consumer_group_exists() 方法:")
        print("-" * 40)
        
        target_group = kafka_config.get('group_id', 'moye-check-data')
        print(f"目标消费者组: {target_group}")
        
        exists, check_info = group_manager.consumer_group_exists(target_group)
        print(f"存在性检查结果: exists={exists}, info={check_info}")
        
        print("\n4. 测试备用检查方法:")
        print("-" * 40)
        
        exists_fallback, check_info_fallback = group_manager.consumer_group_exists_fallback(target_group)
        print(f"备用检查结果: exists={exists_fallback}, info={check_info_fallback}")
        
        print("\n5. 对比两种方法的结果:")
        print("-" * 40)
        
        if exists == exists_fallback:
            print("✅ 两种方法结果一致")
        else:
            print("❌ 两种方法结果不一致")
            print(f"  主方法: {exists} ({check_info})")
            print(f"  备用方法: {exists_fallback} ({check_info_fallback})")
        
        group_manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_manual_parsing():
    """手动测试不同的解析方法"""
    logger = setup_logging()
    
    print("\n🛠️  手动测试数据解析")
    print("=" * 60)
    
    try:
        # 加载配置
        config_manager = ConfigManager("filter_config.yaml")
        kafka_config = config_manager.get_kafka_config()
        
        # 创建消费者组管理器
        group_manager = KafkaConsumerGroupManager(
            bootstrap_servers=kafka_config['bootstrap_servers']
        )
        
        # 获取原始数据
        all_groups = group_manager.admin_client.list_consumer_groups()
        
        print("原始数据分析:")
        print(f"类型: {type(all_groups)}")
        
        if all_groups:
            print(f"长度: {len(all_groups)}")
            
            for i, group in enumerate(all_groups):
                print(f"\n--- 元素 {i} ---")
                print(f"类型: {type(group)}")
                print(f"内容: {group}")
                
                # 尝试不同的访问方法
                group_id = None
                
                print("尝试不同的访问方法:")
                
                # 方法1: 对象属性
                if hasattr(group, 'group_id'):
                    group_id = group.group_id
                    print(f"  ✅ group.group_id = {group_id}")
                else:
                    print(f"  ❌ 没有 group_id 属性")
                
                # 方法2: tuple索引
                if isinstance(group, tuple):
                    if len(group) > 0:
                        print(f"  ✅ tuple[0] = {group[0]}")
                        if not group_id:
                            group_id = group[0]
                    if len(group) > 1:
                        print(f"  ℹ️  tuple[1] = {group[1]}")
                else:
                    print(f"  ❌ 不是 tuple 类型")
                
                # 方法3: dict访问
                if isinstance(group, dict):
                    for key in ['group_id', 'groupId', 'id', 'name']:
                        if key in group:
                            print(f"  ✅ dict['{key}'] = {group[key]}")
                            if not group_id:
                                group_id = group[key]
                else:
                    print(f"  ❌ 不是 dict 类型")
                
                # 方法4: 其他属性
                for attr in ['groupId', 'id', 'name']:
                    if hasattr(group, attr):
                        value = getattr(group, attr)
                        print(f"  ✅ group.{attr} = {value}")
                        if not group_id:
                            group_id = value
                
                print(f"最终提取的group_id: {group_id}")
                
                # 只分析前3个元素
                if i >= 2:
                    break
        
        group_manager.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 list_consumer_groups 返回格式测试")
    print("=" * 80)
    
    tests = [
        ("返回格式测试", test_list_consumer_groups_format),
        ("手动解析测试", test_manual_parsing)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔬 开始测试: {test_name}")
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
                
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "=" * 80)
    print("测试结果汇总:")
    print("=" * 80)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20} {status}")
        if result:
            passed += 1
    
    print("=" * 80)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n📝 下一步:")
        print("1. 现在可以运行 python main.py filter_config.yaml 测试自动清理")
        print("2. 或者运行 python test_consumer_group_detection.py 进行完整测试")
        return True
    else:
        print("⚠️  部分测试失败，请检查错误信息")
        return False


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试执行失败: {e}")
        sys.exit(1)
