# Kafka数据过滤程序配置文件
# 
# 此配置文件定义了Kafka连接参数、过滤规则和输出设置
# 请根据实际环境修改相应配置

# Kafka连接配置
kafka:
  # Kafka集群地址，多个地址用逗号分隔
  bootstrap_servers: "123.235.23.238:19301"

  # 要消费的topic
  topic: "moyeT_dfycb_website"

  # 消费者组ID
  group_id: "moye-check-data"

  # 消费起始位置：earliest（从最早消息开始）或 latest（从最新消息开始）
  auto_offset_reset: "latest"
  
  # 是否自动提交offset
  enable_auto_commit: true

  # 是否强制从最新消息开始消费（忽略已提交的offset）
  # 注意：启用此选项会导致跳过所有历史消息
  force_latest: false
  
  # 安全配置（可选）
  # security_protocol: "SASL_PLAINTEXT"
  # sasl_mechanism: "PLAIN"
  # sasl_username: "your_username"
  # sasl_password: "your_password"

# 过滤规则配置
filter:
  # 过滤规则列表，支持多个规则（OR逻辑：任一规则通过即可）
  rules:
    # 示例规则1：基本字段过滤
    #    - "字段1 = 1 AND 字段2.class2 NOT IN (\"广告信息\", \"股票资讯\", \"新闻合集\", \"历史科普\", \"明星娱乐\") AND 字段3.nature IN (\"境外媒体\", \"党媒\")"
    - "y_is_first_gather = 1 AND n_content_spam.class2 NOT IN (\"广告信息\", \"股票资讯\", \"新闻合集\", \"历史科普\", \"明星娱乐\") AND y_yq_focus.label_name = \"关注\" AND n_media.nature IN (\"境外媒体\", \"党媒\",\"政府部门\")"
    # 示例规则2：数值比较
    # - "score > 80 AND category = \"重要\" AND status NOT IN (\"已删除\", \"草稿\")"
    - "y_is_first_gather = 1 AND n_content_spam.class2 NOT IN (\"广告信息\", \"股票资讯\", \"新闻合集\", \"历史科普\", \"明星娱乐\") AND y_yq_focus.label_name = \"不关注\" AND y_yq_focus.probability < 0.8 AND n_media.nature IN (\"境外媒体\", \"党媒\",\"政府部门\")"
    
    # 示例规则3：字符串包含
    # - "title CONTAINS \"重要\" AND content CONTAINS \"通知\""
    
    # 示例规则4：复杂逻辑组合
    # - "(priority = \"高\" OR urgency = \"紧急\") AND (department IN (\"技术部\", \"产品部\") OR level >= 5)"

# 输出配置
output:
  # 输出类型：console（控制台）、file（文件）、both（同时输出到控制台和文件）
  type: "both"
  
  # 输出格式：json（紧凑JSON）、pretty_json（格式化JSON）、text（文本格式）
  format: "pretty_json"
  
  # 文件输出路径（当type为file或both时必需）
  file_path: "filtered_messages3.json"
  
  # 是否显示过滤详情
  show_filter_details: false

# 统计配置
statistics:
  # 是否启用统计功能
  enabled: true
  
  # 统计报告输出间隔（秒）
  interval: 10
  
  # 是否显示详细统计信息
  show_details: true

# 日志配置
logging:
  # 日志级别：DEBUG、INFO、WARNING、ERROR、CRITICAL
  level: "INFO"

# 自动清理配置
auto_cleanup:
  # 是否启用程序退出时自动删除消费者组
  enabled: true

  # 清理操作超时时间（秒）
  timeout: 30

  # 清理前是否备份消费者组信息
  backup_before_cleanup: true

  # 是否在异常退出时也执行清理（建议启用）
  cleanup_on_signal: true
