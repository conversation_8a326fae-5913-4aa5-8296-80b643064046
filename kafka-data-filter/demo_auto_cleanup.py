#!/usr/bin/env python3
"""
自动清理功能演示脚本

演示完整的自动清理流程，包括：
1. 程序启动和消费者组创建
2. 模拟数据处理
3. 程序退出和自动清理
"""

import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from kafka_data_filter.main import KafkaDataFilterApp
from kafka_data_filter.kafka_consumer_group_manager import KafkaConsumerGroupManager
from kafka_data_filter.config_manager import ConfigManager


def setup_logging():
    """配置日志系统"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('demo_auto_cleanup.log', encoding='utf-8')
        ]
    )
    return logging.getLogger(__name__)


def check_consumer_group_exists(config_path: str, group_id: str) -> bool:
    """
    检查消费者组是否存在
    
    参数:
        config_path: 配置文件路径
        group_id: 消费者组ID
        
    返回:
        是否存在
    """
    try:
        config_manager = ConfigManager(config_path)
        kafka_config = config_manager.get_kafka_config()
        
        group_manager = KafkaConsumerGroupManager(
            bootstrap_servers=kafka_config['bootstrap_servers']
        )
        
        group_info = group_manager.get_consumer_group_info(group_id)
        exists = group_info.get('exists', False)
        
        group_manager.close()
        return exists
        
    except Exception as e:
        print(f"检查消费者组状态失败: {e}")
        return False


def demo_complete_lifecycle():
    """演示完整的生命周期"""
    logger = setup_logging()
    config_path = "filter_config.yaml"
    
    # 从配置文件获取消费者组ID
    config_manager = ConfigManager(config_path)
    kafka_config = config_manager.get_kafka_config()
    group_id = kafka_config.get('group_id', 'moye-check-data')
    
    print("🚀 Kafka消费者组自动清理功能演示")
    print("=" * 60)
    
    # 步骤1: 检查初始状态
    print("步骤1: 检查消费者组初始状态")
    initial_exists = check_consumer_group_exists(config_path, group_id)
    print(f"消费者组 {group_id} 初始状态: {'存在' if initial_exists else '不存在'}")
    
    # 步骤2: 启动应用程序
    print("\n步骤2: 启动Kafka数据过滤程序")
    try:
        app = KafkaDataFilterApp(config_path)
        print("✅ 应用程序启动成功")
        
        # 检查自动清理状态
        cleanup_status = app.get_cleanup_status()
        print("自动清理配置:")
        for key, value in cleanup_status.items():
            print(f"  {key}: {value}")
        
    except Exception as e:
        print(f"❌ 应用程序启动失败: {e}")
        return
    
    # 步骤3: 验证消费者组已创建
    print("\n步骤3: 验证消费者组已创建")
    time.sleep(2)  # 等待消费者组创建
    after_start_exists = check_consumer_group_exists(config_path, group_id)
    print(f"消费者组 {group_id} 启动后状态: {'存在' if after_start_exists else '不存在'}")
    
    # 步骤4: 模拟运行一段时间
    print("\n步骤4: 模拟程序运行")
    print("程序将运行5秒钟，模拟正常的数据处理...")
    
    for i in range(5):
        print(f"  运行中... {i+1}/5 秒")
        time.sleep(1)
    
    # 步骤5: 正常停止程序
    print("\n步骤5: 正常停止程序")
    print("执行优雅关闭，自动清理功能将被触发...")
    
    try:
        # 这里会触发自动清理
        app.stop()
        print("✅ 程序已正常停止")
        
    except Exception as e:
        print(f"❌ 程序停止时发生错误: {e}")
    
    # 步骤6: 验证消费者组已被清理
    print("\n步骤6: 验证消费者组已被自动清理")
    time.sleep(3)  # 等待清理完成
    
    final_exists = check_consumer_group_exists(config_path, group_id)
    print(f"消费者组 {group_id} 最终状态: {'存在' if final_exists else '不存在'}")
    
    # 步骤7: 总结结果
    print("\n" + "=" * 60)
    print("演示结果总结:")
    print(f"  初始状态: {'存在' if initial_exists else '不存在'}")
    print(f"  启动后状态: {'存在' if after_start_exists else '不存在'}")
    print(f"  最终状态: {'存在' if final_exists else '不存在'}")
    
    if after_start_exists and not final_exists:
        print("✅ 自动清理功能工作正常！")
        print("   消费者组在程序启动时创建，在程序退出时自动删除")
    elif not after_start_exists:
        print("⚠️  消费者组未能正常创建，可能是连接问题")
    elif final_exists:
        print("❌ 自动清理功能未生效，消费者组仍然存在")
        print("   请检查配置和日志文件")
    
    print("=" * 60)


def demo_manual_cleanup():
    """演示手动清理功能"""
    logger = setup_logging()
    config_path = "filter_config.yaml"
    
    print("🔧 手动清理功能演示")
    print("=" * 60)
    
    try:
        # 启动应用程序
        app = KafkaDataFilterApp(config_path)
        print("✅ 应用程序启动成功")
        
        # 获取消费者组ID
        cleanup_status = app.get_cleanup_status()
        group_id = cleanup_status.get('group_id', 'unknown')
        
        # 检查消费者组状态
        print(f"\n检查消费者组 {group_id} 状态...")
        exists_before = check_consumer_group_exists(config_path, group_id)
        print(f"清理前状态: {'存在' if exists_before else '不存在'}")
        
        if exists_before:
            # 执行手动清理
            print("\n执行手动清理...")
            success = app.manual_cleanup_consumer_group(force=True)
            
            if success:
                print("✅ 手动清理成功")
                
                # 验证清理结果
                time.sleep(2)
                exists_after = check_consumer_group_exists(config_path, group_id)
                print(f"清理后状态: {'存在' if exists_after else '不存在'}")
                
                if not exists_after:
                    print("✅ 消费者组已成功删除")
                else:
                    print("⚠️  消费者组仍然存在，可能需要更多时间")
            else:
                print("❌ 手动清理失败")
        else:
            print("消费者组不存在，无需清理")
        
        # 正常停止程序（禁用自动清理避免重复）
        if hasattr(app, 'auto_cleanup_manager') and app.auto_cleanup_manager:
            app.auto_cleanup_manager.disable_auto_cleanup()
        
        app.stop()
        
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
    
    print("=" * 60)


def demo_independent_cleanup():
    """演示独立清理脚本"""
    print("🛠️  独立清理脚本演示")
    print("=" * 60)
    
    import subprocess
    
    try:
        # 显示可用的消费者组
        print("1. 查看当前消费者组列表:")
        result = subprocess.run([
            sys.executable, "consumer_group_admin.py",
            "--config", "filter_config.yaml", "list"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print(result.stdout)
        else:
            print(f"查看消费者组失败: {result.stderr}")
        
        # 演示独立清理
        print("\n2. 使用独立脚本清理消费者组:")
        print("命令: python cleanup_consumer_group.py --config filter_config.yaml --dry-run")
        
        result = subprocess.run([
            sys.executable, "cleanup_consumer_group.py",
            "--config", "filter_config.yaml", "--dry-run"
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 独立清理脚本运行成功（试运行模式）")
            print(result.stdout)
        else:
            print(f"❌ 独立清理脚本运行失败: {result.stderr}")
        
    except subprocess.TimeoutExpired:
        print("⏰ 命令执行超时")
    except Exception as e:
        print(f"演示过程中发生错误: {e}")
    
    print("=" * 60)


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="自动清理功能演示",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
演示模式:
  lifecycle   - 完整生命周期演示（推荐）
  manual      - 手动清理功能演示
  independent - 独立清理脚本演示
  all         - 运行所有演示
        """
    )
    
    parser.add_argument(
        "mode",
        choices=["lifecycle", "manual", "independent", "all"],
        nargs="?",
        default="lifecycle",
        help="演示模式（默认: lifecycle）"
    )
    
    args = parser.parse_args()
    
    print("🎭 Kafka消费者组自动清理功能演示程序")
    print(f"演示模式: {args.mode}")
    print()
    
    try:
        if args.mode == "lifecycle":
            demo_complete_lifecycle()
        
        elif args.mode == "manual":
            demo_manual_cleanup()
        
        elif args.mode == "independent":
            demo_independent_cleanup()
        
        elif args.mode == "all":
            demo_complete_lifecycle()
            print("\n" + "🔄" * 20)
            demo_manual_cleanup()
            print("\n" + "🔄" * 20)
            demo_independent_cleanup()
        
        print("\n🎉 演示完成！")
        print("请查看日志文件 demo_auto_cleanup.log 获取详细信息")
        
    except KeyboardInterrupt:
        print("\n演示被用户中断")
    except Exception as e:
        print(f"演示执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
